import React, { useEffect, useState } from 'react';
import {
  Box,
  Button,
  Flex,
  Input,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  TableContainer
} from '@chakra-ui/react';
import { FiChevronDown, FiFilter } from 'react-icons/fi';
import axios from "axios";
import { getAllCountries } from '../../../services/marketDataServices/CountryService';

const countryImage = "data:image/png;base64,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"


const EconomicCalendar = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [economicData, setEconomicData] = useState([]);
  const [selectedImpact, setSelectedImpact] = useState("");
  const [countries, setCountries] = useState({});


  useEffect(() => {
    const API_TOKEN = process.env.REACT_APP_BEARER_TOKEN
    const fetchEconomicData = async () => {
      try {
        const response = await axios.get("https://api.xcapitalterminal.com/api/v1/macro-data/economic-calendar", {
          headers: {
            Authorization: `Bearer ${API_TOKEN}`,
          },
        });
        setEconomicData(response.data);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    getAllCountries().then(response => {
        const countryMap = response.data.reduce((acc, country) => {
          acc[country.id] = country.name;
          return acc;
        }, {});
        setCountries(countryMap);

    }).catch(error => {
        console.log(error)
    })
 
    
    fetchEconomicData();
  }, []);

  const filteredData = economicData.filter((item) => {
    const matchesQuery = item.event.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesImpact = selectedImpact ? item.impact === selectedImpact : true;
    return matchesQuery && matchesImpact;
  });

  return (
    <Box bg="black" color="white" ml="auto" mr="auto">
      <Box bg={"#1f1f1f"} textAlign="center" w='100%' h="40px" p={2} mt={"40px"} fontSize={"18px"}
        color='white'>
        Economic calendar
      </Box>
      <Box bg={"#171717"} w='100%' p={2} fontSize={"18px"} color='white'>
      
        <Flex>

         <Menu>
            <MenuButton
              as={Button}
              height={'35px'}
              bgColor="#161616"
              color="white"
              borderWidth={"1px 0 1px 1px"}
              borderRadius={"5px 0 0px 5px"}
              borderColor={"rgba(119,119,119,0.5)"}
              _hover={{ bgColor: "#242424" }}
              rightIcon={<FiChevronDown />}
              _focus={{ bgColor: "#242424" }}
            >
              <Flex>
                <Box m={"5px 10px 0 0"} fontSize={'12px'}>
                  <FiFilter fill={"white"} />
                </Box>
                All
              </Flex>
            </MenuButton>
            <MenuList bgColor="#121212" borderColor={"transparent"} minWidth={'100px'} >
              <MenuItem bgColor="#121212" _hover={{ bgColor: "#242424" }}  onClick={() => setSelectedImpact("High")}>High</MenuItem>
              <MenuItem bgColor="#121212" _hover={{ bgColor: "#242424" }}  onClick={() => setSelectedImpact("Medium")}>Medium</MenuItem>
              <MenuItem bgColor="#121212" _hover={{ bgColor: "#242424" }}  onClick={() => setSelectedImpact("Low")}>Low</MenuItem>
            </MenuList>
          </Menu>
         <Input
           placeholder="Search by event..."
           value={searchQuery}
           onChange={(e) => setSearchQuery(e.target.value)}
           width={'300px'}
           height={'35px'}
           borderColor="rgba(119,119,119,0.5)"
          borderWidth="1px"
          borderRadius="0px 5px 5px 0px"
         />

         </Flex>
       </Box>

      <TableContainer>
        <Table variant="simple" colorScheme="whiteAlpha" bgColor="#0A0A0A">
          <Thead className='table-header'>
            <Tr>
              <Th>Time</Th>
              <Th>Country</Th>
              <Th>Impact</Th>
              <Th>Event</Th>
              <Th>Actual</Th>
              <Th>Forecast</Th>
              <Th>Previous</Th>
              <Th>Unit</Th>
            </Tr>
          </Thead>
          <Tbody>
            {filteredData.map((item, index) => (
              <Tr key={index}>
                <Td>{item.date}</Td>
                {/* <Td>
                  <Box width="30px" height="20px" bg="gray.800" borderRadius="md">
                    <img src={item.country} alt="Country Flag" style={{ width: '100%', height: '100%' }} />
                  </Box>
                </Td> */}
                 <Td>{countries[item.country] || 'Unknown Country'}</Td>
                {/* <Td>
                  {Array(item.impact).fill().map((_, i) => (
                    <span key={i}>⭐</span>
                  ))}
                </Td> 
                <Td>{item.impact === "High" ? "★★★" : item.impact === "Medium" ? "★★" : "★"}</Td>
                */}
                <Td>{item.impact}</Td>
                <Td>{item.event}</Td>
                <Td>{item.actual} %</Td>
                <Td>{item.forecast} %</Td>
                <Td>{item.previous} %</Td>
                <Td>%</Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default EconomicCalendar;
