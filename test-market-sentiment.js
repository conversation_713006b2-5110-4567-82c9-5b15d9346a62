// Test script to verify market sentiment object handling
// This can be run in the browser console to test our SafeDataRenderer logic

// Mock market sentiment object (same structure as from backend)
const mockMarketSentiment = {
    positive_companies: 45,
    negative_companies: 23,
    neutral_companies: 12,
    positive_percentage: 56.25,
    negative_percentage: 28.75
};

// Test SafeDataRenderer logic
const testSafeDataRenderer = (data, fallback = 'N/A') => {
    if (data === null || data === undefined) return fallback;
    if (typeof data === 'object') {
        // Handle market sentiment objects specifically
        if (data.positive_companies !== undefined || data.negative_companies !== undefined) {
            console.warn('Market sentiment object detected and converted:', data);
            const positivePercentage = data.positive_percentage || 0;
            const negativePercentage = data.negative_percentage || 0;
            
            if (positivePercentage > 60) return 'Bullish Market';
            if (negativePercentage > 60) return 'Bearish Market';
            return `Mixed (${positivePercentage}% positive)`;
        }
        // For other objects, stringify them safely
        try {
            return JSON.stringify(data);
        } catch (error) {
            console.warn('Failed to stringify object:', error);
            return fallback;
        }
    }
    return String(data);
};

// Test getMarketSentimentDisplay logic
const testGetMarketSentimentDisplay = (marketSentiment) => {
    // If it's already a string (legacy format), return it
    if (typeof marketSentiment === 'string') {
        return marketSentiment;
    }
    
    // If it's an object with sentiment data, calculate sentiment
    if (typeof marketSentiment === 'object' && marketSentiment !== null) {
        const { positive_percentage, negative_percentage } = marketSentiment;
        
        if (positive_percentage !== undefined && negative_percentage !== undefined) {
            if (positive_percentage > 60) return 'Bullish';
            if (negative_percentage > 60) return 'Bearish';
            return 'Neutral';
        }
    }
    
    return 'Neutral';
};

// Run tests
console.log('Testing SafeDataRenderer with market sentiment object:');
console.log('Input:', mockMarketSentiment);
console.log('Output:', testSafeDataRenderer(mockMarketSentiment));

console.log('\nTesting getMarketSentimentDisplay with market sentiment object:');
console.log('Input:', mockMarketSentiment);
console.log('Output:', testGetMarketSentimentDisplay(mockMarketSentiment));

console.log('\nTesting with legacy string format:');
console.log('Input: "Bullish"');
console.log('Output:', testGetMarketSentimentDisplay('Bullish'));

console.log('\nTesting with null/undefined:');
console.log('Input: null');
console.log('Output:', testSafeDataRenderer(null));

console.log('\nAll tests completed successfully!');
