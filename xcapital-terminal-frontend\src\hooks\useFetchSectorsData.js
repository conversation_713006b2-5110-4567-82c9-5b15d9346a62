import { useState, useEffect, useCallback } from 'react';

// Importing URL and token from the environment variables
const url = process.env.REACT_APP_API_SECTORS;   
const token = process.env.REACT_APP_BEARER_TOKEN;

const useFetchSectorsData = () => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const fetchData = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: token ? `Bearer ${token}` : undefined,
                },
            });

            if (!response.ok) {
                throw new Error(`Failed to fetch data: ${response.statusText}`);
            }

            const result = await response.json();
            setData(result);
        } catch (error) {
            setError(error.message || 'An unknown error occurred');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        if (url) {
            fetchData();
        }
    }, [fetchData]);

    return { data, loading, error, refetch: fetchData };
};

export default useFetchSectorsData;
