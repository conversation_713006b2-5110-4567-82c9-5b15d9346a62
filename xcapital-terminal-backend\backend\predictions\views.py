from rest_framework import generics, status, filters
from rest_framework.decorators import api_view
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta, date
import pandas as pd
import numpy as np
import pickle
import os
from pathlib import Path
import logging

from .models import PredictionModel, Prediction, PredictionRequest
from market_data.models import Stock, StockPrice
from .serializers import (
    PredictionModelSerializer, PredictionSerializer, PredictionRequestSerializer,
    CreatePredictionRequestSerializer, PredictionResultSerializer, ModelInfoSerializer
)

logger = logging.getLogger(__name__)

# Configuration des chemins
MODELS_DIR = Path("models/Arima")  # Chemin vers les modèles ARIMA
BASE_DIR = Path(__file__).resolve().parent.parent


class ARIMAModelLoader:
    """Service pour charger et utiliser les modèles ARIMA pré-entraînés"""
    
    def __init__(self):
        self.models_cache = {}
        self.models_dir = BASE_DIR / MODELS_DIR
        
    def get_available_models(self):
        """Récupère la liste des modèles ARIMA disponibles"""
        try:
            if not self.models_dir.exists():
                return []
            
            model_files = list(self.models_dir.glob("*.pkl"))
            tickers = []
            
            for model_file in model_files:
                # Extraire le ticker du nom de fichier
                # Format attendu: TICKER_arima_model.pkl ou TICKER.pkl
                ticker = model_file.stem.replace("_arima_model", "").replace("_model", "").upper()
                tickers.append(ticker)
                
            return sorted(tickers)
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des modèles: {e}")
            return []
    
    def load_model(self, ticker):
        """Charge un modèle ARIMA pour un ticker donné"""
        if ticker in self.models_cache:
            return self.models_cache[ticker]
        
        try:
            # Essayer différents formats de noms de fichiers
            possible_names = [
                f"{ticker}_arima_model.pkl",
                f"{ticker}_model.pkl", 
                f"{ticker}.pkl",
                f"{ticker.lower()}_arima_model.pkl",
                f"{ticker.lower()}_model.pkl",
                f"{ticker.lower()}.pkl"
            ]
            
            model_path = None
            for name in possible_names:
                path = self.models_dir / name
                if path.exists():
                    model_path = path
                    break
            
            if not model_path:
                raise FileNotFoundError(f"Modèle ARIMA non trouvé pour {ticker}")
            
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
            
            self.models_cache[ticker] = model
            logger.info(f"Modèle ARIMA chargé pour {ticker}")
            return model
            
        except Exception as e:
            logger.error(f"Erreur lors du chargement du modèle pour {ticker}: {e}")
            raise
    
    def predict(self, ticker, forecast_days=30, confidence_level=0.95):
        """Effectue une prédiction avec le modèle ARIMA"""
        try:
            model = self.load_model(ticker)
            
            # Effectuer la prédiction
            forecast_result = model.forecast(steps=forecast_days, alpha=(1-confidence_level))
            
            if hasattr(forecast_result, 'predicted_mean'):
                # Format statsmodels récent
                forecast_values = forecast_result.predicted_mean.values
                if hasattr(forecast_result, 'conf_int'):
                    conf_int = forecast_result.conf_int()
                    confidence_lower = conf_int.iloc[:, 0].values
                    confidence_upper = conf_int.iloc[:, 1].values
                else:
                    # Estimation simple si pas d'intervalle de confiance
                    std_error = np.std(forecast_values) * 0.1
                    confidence_lower = forecast_values - 1.96 * std_error
                    confidence_upper = forecast_values + 1.96 * std_error
            else:
                # Format statsmodels ancien ou autre format
                if isinstance(forecast_result, tuple) and len(forecast_result) >= 3:
                    forecast_values = forecast_result[0]
                    confidence_lower = forecast_result[2][:, 0]
                    confidence_upper = forecast_result[2][:, 1]
                else:
                    forecast_values = np.array(forecast_result)
                    std_error = np.std(forecast_values) * 0.1
                    confidence_lower = forecast_values - 1.96 * std_error
                    confidence_upper = forecast_values + 1.96 * std_error
            
            return {
                'forecast_values': forecast_values.tolist(),
                'confidence_lower': confidence_lower.tolist(),
                'confidence_upper': confidence_upper.tolist()
            }
            
        except Exception as e:
            logger.error(f"Erreur lors de la prédiction pour {ticker}: {e}")
            raise

# Instance globale du chargeur de modèles
arima_loader = ARIMAModelLoader()


class PredictionModelListView(generics.ListAPIView):
    """API view to list prediction models"""
    queryset = PredictionModel.objects.filter(is_active=True)
    serializer_class = PredictionModelSerializer
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['stock__ticker', 'model_type', 'name']
    ordering_fields = ['created_at', 'model_type']
    ordering = ['-created_at']


class PredictionListView(generics.ListAPIView):
    """API view to list predictions for a specific stock and model"""
    serializer_class = PredictionSerializer
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ['target_date', 'prediction_date']
    ordering = ['-target_date']

    def get_queryset(self):
        ticker = self.kwargs.get('ticker')
        model_type = self.request.query_params.get('model_type')
        
        queryset = Prediction.objects.select_related('model', 'model__stock')
        
        if ticker:
            queryset = queryset.filter(model__stock__ticker=ticker)
        
        if model_type:
            queryset = queryset.filter(model__model_type=model_type)
            
        return queryset


@api_view(['GET'])
def get_model_info(request, ticker):
    """API endpoint to get information about available models for a ticker"""
    try:
        stock = Stock.objects.get(ticker=ticker, is_active=True)
        models = PredictionModel.objects.filter(stock=stock, is_active=True)
        
        if not models:
            return Response(
                {'error': f'No models found for ticker {ticker}'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        model_details = {}
        available_models = []
        
        for model in models:
            available_models.append(model.model_type)
            model_details[model.model_type] = {
                'name': model.name,
                'parameters': model.parameters,
                'accuracy_metrics': model.accuracy_metrics,
                'training_period': {
                    'start': model.training_start_date,
                    'end': model.training_end_date
                },
                'created_at': model.created_at,
                'updated_at': model.updated_at
            }
        
        result = {
            'ticker': ticker,
            'available_models': available_models,
            'model_details': model_details,
            'last_updated': max(models, key=lambda x: x.updated_at).updated_at,
            'training_period': {
                'earliest_start': min(models, key=lambda x: x.training_start_date).training_start_date,
                'latest_end': max(models, key=lambda x: x.training_end_date).training_end_date
            },
            'performance_metrics': {
                model.model_type: model.accuracy_metrics 
                for model in models if model.accuracy_metrics
            }
        }
        
        return Response(result)
        
    except Stock.DoesNotExist:
        return Response(
            {'error': f'Stock with ticker {ticker} not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )


@api_view(['POST'])
def create_prediction_request(request):
    """API endpoint to create a new prediction request"""
    serializer = CreatePredictionRequestSerializer(data=request.data)
    if serializer.is_valid():
        data = serializer.validated_data
        
        try:
            stock = Stock.objects.get(ticker=data['ticker'], is_active=True)
        except Stock.DoesNotExist:
            return Response(
                {'error': f'Stock with ticker {data["ticker"]} not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Create prediction request
        prediction_request = PredictionRequest.objects.create(
            stock=stock,
            model_type=data['model_type'],
            start_date=data['start_date'],
            end_date=data['end_date'],
            forecast_days=data['forecast_days'],
            confidence_interval=data['confidence_interval']
        )
        
        # Process the prediction (in a real implementation, this would be done asynchronously)
        try:
            result = process_prediction_request(prediction_request)
            prediction_request.status = 'COMPLETED'
            prediction_request.result_data = result
            prediction_request.completed_at = datetime.now()
            prediction_request.save()
            
            return Response(result)
            
        except Exception as e:
            prediction_request.status = 'FAILED'
            prediction_request.error_message = str(e)
            prediction_request.save()
            
            return Response(
                {'error': f'Prediction failed: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


def process_prediction_request(prediction_request):
    """Process a prediction request and return results"""
    # This is a simplified version - in reality, you'd load your trained models
    # and perform actual predictions
    
    stock = prediction_request.stock
    model_type = prediction_request.model_type
    
    # Get historical data
    historical_data = StockPrice.objects.filter(
        stock=stock,
        date__gte=prediction_request.start_date,
        date__lte=prediction_request.end_date
    ).order_by('date')
    
    if not historical_data:
        raise ValueError(f"No historical data found for {stock.ticker}")
    
    # Generate mock predictions for demonstration
    # In a real implementation, you would load your trained ARIMA/ML models here
    forecast_dates = []
    forecast_values = []
    confidence_lower = []
    confidence_upper = []
    
    last_price = float(historical_data.last().close_price)
    base_date = prediction_request.end_date
    
    for i in range(prediction_request.forecast_days):
        forecast_date = base_date + timedelta(days=i+1)
        # Mock prediction logic - replace with actual model predictions
        trend = 0.001 * i  # Small upward trend
        noise = np.random.normal(0, 0.02)  # Random noise
        predicted_value = last_price * (1 + trend + noise)
        
        confidence_range = predicted_value * 0.05  # 5% confidence interval
        
        forecast_dates.append(forecast_date.isoformat())
        forecast_values.append(round(predicted_value, 4))
        confidence_lower.append(round(predicted_value - confidence_range, 4))
        confidence_upper.append(round(predicted_value + confidence_range, 4))
    
    # Generate test data (last 10% of historical data for backtesting)
    test_size = max(1, len(historical_data) // 10)
    test_data = historical_data[len(historical_data)-test_size:]
    
    test_dates = [data.date.isoformat() for data in test_data]
    test_actuals = [float(data.close_price) for data in test_data]
    test_predictions = [float(data.close_price) * (1 + np.random.normal(0, 0.01)) for data in test_data]
    
    # Calculate mock metrics
    mae = np.mean(np.abs(np.array(test_actuals) - np.array(test_predictions)))
    rmse = np.sqrt(np.mean((np.array(test_actuals) - np.array(test_predictions))**2))
    mape = np.mean(np.abs((np.array(test_actuals) - np.array(test_predictions)) / np.array(test_actuals))) * 100
    
    model_metrics = {
        'mae': round(mae, 4),
        'rmse': round(rmse, 4),
        'mape': round(mape, 4),
        'r_squared': 0.85  # Mock R-squared
    }
    
    return {
        'ticker': stock.ticker,
        'model_type': model_type,
        'forecast_dates': forecast_dates,
        'forecast_values': forecast_values,
        'confidence_lower': confidence_lower,
        'confidence_upper': confidence_upper,
        'test_dates': test_dates,
        'test_actuals': test_actuals,
        'test_predictions': test_predictions,
        'model_metrics': model_metrics,
        'status': 'success',
        'message': f'Prediction completed for {stock.ticker} using {model_type} model'
    }


@api_view(['GET'])
def arima_predict(request):
    """
    API endpoint compatible avec l'ancien système FastAPI
    GET /api/v1/arima-predict?ticker=TICKER&start_date=DATE&end_date=DATE&forecast_days=30
    """
    try:
        # Récupération des paramètres
        ticker = request.GET.get('ticker')
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date') 
        forecast_days = int(request.GET.get('forecast_days', 30))
        confidence_interval = float(request.GET.get('confidence_interval', 95.0))
        
        if not ticker:
            return Response(
                {'error': 'Le paramètre ticker est requis'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        ticker = ticker.upper()
        
        # Vérifier que le stock existe
        try:
            stock = Stock.objects.get(ticker=ticker, is_active=True)
        except Stock.DoesNotExist:
            return Response(
                {'error': f'Stock avec ticker {ticker} non trouvé'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Vérifier que le modèle ARIMA existe
        if ticker not in arima_loader.get_available_models():
            return Response(
                {'error': f'Modèle ARIMA non disponible pour {ticker}'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Effectuer la prédiction
        prediction_result = arima_loader.predict(
            ticker=ticker,
            forecast_days=forecast_days,
            confidence_level=confidence_interval/100
        )
        
        # Générer les dates de prédiction
        if end_date:
            base_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        else:
            # Utiliser la dernière date disponible dans les données
            latest_price = StockPrice.objects.filter(stock=stock).order_by('-date').first()
            base_date = latest_price.date if latest_price else date.today()
        
        forecast_dates = []
        for i in range(forecast_days):
            forecast_date = base_date + timedelta(days=i+1)
            forecast_dates.append(forecast_date.isoformat())
        
        # Récupérer les données de test (derniers 10% des données historiques)
        historical_data = StockPrice.objects.filter(stock=stock).order_by('date')
        if start_date:
            historical_data = historical_data.filter(date__gte=start_date)
        if end_date:
            historical_data = historical_data.filter(date__lte=end_date)
            
        test_size = max(1, len(historical_data) // 10)
        test_data = historical_data[len(historical_data)-test_size:]
        
        test_dates = [data.date.isoformat() for data in test_data]
        test_actuals = [float(data.close_price) for data in test_data]
        
        # Générer des prédictions de test (simulation pour la démo)
        test_predictions = []
        for actual in test_actuals:
            noise = np.random.normal(0, actual * 0.02)  # 2% de bruit
            test_predictions.append(max(0, actual + noise))
        
        # Calculer les métriques
        if test_actuals and test_predictions:
            mae = np.mean(np.abs(np.array(test_actuals) - np.array(test_predictions)))
            rmse = np.sqrt(np.mean((np.array(test_actuals) - np.array(test_predictions))**2))
            mape = np.mean(np.abs((np.array(test_actuals) - np.array(test_predictions)) / np.array(test_actuals))) * 100
            
            # Calculer R²
            ss_res = np.sum((np.array(test_actuals) - np.array(test_predictions))**2)
            ss_tot = np.sum((np.array(test_actuals) - np.mean(test_actuals))**2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        else:
            mae = rmse = mape = r_squared = 0
        
        # Format de réponse compatible avec l'ancien API
        response_data = {
            'ticker': ticker,
            'model_type': 'ARIMA',
            'forecast_dates': forecast_dates,
            'forecast_values': [round(x, 4) for x in prediction_result['forecast_values']],
            'confidence_lower': [round(x, 4) for x in prediction_result['confidence_lower']],
            'confidence_upper': [round(x, 4) for x in prediction_result['confidence_upper']],
            'test_dates': test_dates,
            'test_actuals': [round(x, 4) for x in test_actuals],
            'test_predictions': [round(x, 4) for x in test_predictions],
            'model_metrics': {
                'mae': round(mae, 4),
                'rmse': round(rmse, 4),
                'mape': round(mape, 4),
                'r_squared': round(r_squared, 4)
            },
            'status': 'success',
            'message': f'Prédiction ARIMA complétée pour {ticker}'
        }
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"Erreur lors de la prédiction ARIMA: {e}")
        return Response(
            {
                'ticker': ticker if 'ticker' in locals() else 'unknown',
                'model_type': 'ARIMA',
                'status': 'error',
                'message': f'Erreur lors de la prédiction: {str(e)}'
            }, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
def daily_price_prediction(request):
    """
    API POST pour prédire le prix jour par jour d'une entreprise
    POST /api/v1/predictions/daily-predict/
    {
        "ticker": "ATTIJARIWAFA_BANK",
        "prediction_days": 30,
        "confidence_level": 95,
        "start_from_date": "2024-12-31" (optionnel)
    }
    """
    try:
        # Récupération des paramètres
        ticker = request.data.get('ticker')
        prediction_days = int(request.data.get('prediction_days', 30))
        confidence_level = float(request.data.get('confidence_level', 95.0))
        start_from_date = request.data.get('start_from_date')
        
        # Validation
        if not ticker:
            return Response(
                {'error': 'Le paramètre ticker est requis'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if prediction_days < 1 or prediction_days > 365:
            return Response(
                {'error': 'prediction_days doit être entre 1 et 365'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if confidence_level < 50 or confidence_level > 99:
            return Response(
                {'error': 'confidence_level doit être entre 50 et 99'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        ticker = ticker.upper()
        
        # Vérifier que le stock existe
        try:
            stock = Stock.objects.get(ticker=ticker, is_active=True)
        except Stock.DoesNotExist:
            return Response(
                {'error': f'Stock avec ticker {ticker} non trouvé'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Vérifier que le modèle ARIMA existe
        if ticker not in arima_loader.get_available_models():
            return Response(
                {'error': f'Modèle ARIMA non disponible pour {ticker}'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Définir la date de départ pour les prédictions
        if start_from_date:
            try:
                base_date = datetime.strptime(start_from_date, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {'error': 'Format de date invalide pour start_from_date. Utilisez YYYY-MM-DD'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            # Utiliser la dernière date disponible dans les données
            latest_price = StockPrice.objects.filter(stock=stock).order_by('-date').first()
            if latest_price:
                base_date = latest_price.date
            else:
                base_date = date.today()
        
        # Effectuer la prédiction ARIMA
        prediction_result = arima_loader.predict(
            ticker=ticker,
            forecast_days=prediction_days,
            confidence_level=confidence_level/100
        )
        
        # Créer les prédictions jour par jour
        daily_predictions = []
        forecast_values = prediction_result['forecast_values']
        confidence_lower = prediction_result['confidence_lower']
        confidence_upper = prediction_result['confidence_upper']
        
        for i in range(prediction_days):
            prediction_date = base_date + timedelta(days=i+1)
            
            daily_prediction = {
                'date': prediction_date.isoformat(),
                'day_number': i + 1,
                'predicted_price': round(forecast_values[i], 4),
                'confidence_lower': round(confidence_lower[i], 4),
                'confidence_upper': round(confidence_upper[i], 4),
                'confidence_level': confidence_level
            }
            
            # Calculer la variation par rapport au jour précédent
            if i > 0:
                prev_price = forecast_values[i-1]
                current_price = forecast_values[i]
                daily_change = current_price - prev_price
                daily_change_pct = (daily_change / prev_price * 100) if prev_price != 0 else 0
                
                daily_prediction['daily_change'] = round(daily_change, 4)
                daily_prediction['daily_change_pct'] = round(daily_change_pct, 4)
            else:
                # Pour le premier jour, comparer avec le dernier prix historique
                if latest_price:
                    historical_price = float(latest_price.close_price)
                    daily_change = forecast_values[i] - historical_price
                    daily_change_pct = (daily_change / historical_price * 100) if historical_price != 0 else 0
                    
                    daily_prediction['daily_change'] = round(daily_change, 4)
                    daily_prediction['daily_change_pct'] = round(daily_change_pct, 4)
            
            daily_predictions.append(daily_prediction)
        
        # Calculer les statistiques globales de la prédiction
        prediction_stats = {
            'total_change': round(forecast_values[-1] - forecast_values[0], 4),
            'total_change_pct': round((forecast_values[-1] - forecast_values[0]) / forecast_values[0] * 100, 4),
            'max_predicted_price': round(max(forecast_values), 4),
            'min_predicted_price': round(min(forecast_values), 4),
            'average_predicted_price': round(sum(forecast_values) / len(forecast_values), 4),
            'volatility': round(np.std(forecast_values), 4)
        }
        
        # Obtenir le dernier prix historique pour contexte
        historical_context = {}
        if latest_price:
            historical_context = {
                'last_historical_date': latest_price.date.isoformat(),
                'last_historical_price': float(latest_price.close_price),
                'last_historical_volume': latest_price.volume
            }
        
        # Réponse complète
        response_data = {
            'ticker': ticker,
            'company_name': stock.name,
            'model_type': 'ARIMA',
            'prediction_parameters': {
                'prediction_days': prediction_days,
                'confidence_level': confidence_level,
                'start_from_date': base_date.isoformat()
            },
            'historical_context': historical_context,
            'daily_predictions': daily_predictions,
            'prediction_statistics': prediction_stats,
            'model_info': {
                'prediction_timestamp': datetime.now().isoformat(),
                'model_path': f'models/Arima/{ticker}_arima_model.pkl'
            },
            'status': 'success',
            'message': f'Prédiction jour par jour complétée pour {ticker} sur {prediction_days} jours'
        }
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"Erreur lors de la prédiction jour par jour: {e}")
        return Response(
            {
                'ticker': ticker if 'ticker' in locals() else 'unknown',
                'status': 'error',
                'message': f'Erreur lors de la prédiction: {str(e)}'
            }, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
def get_prediction_request_status(request, request_id):
    """API endpoint to get the status of a prediction request"""
    try:
        prediction_request = PredictionRequest.objects.get(id=request_id)
        serializer = PredictionRequestSerializer(prediction_request)
        return Response(serializer.data)
    except PredictionRequest.DoesNotExist:
        return Response(
            {'error': 'Prediction request not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
