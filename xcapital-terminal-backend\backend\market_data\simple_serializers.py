from rest_framework import serializers
from .csv_models import FinancialInstrument, InstrumentPrice, MASIIndex, MASIIndexValue, InstrumentPrediction


class SimpleFinancialInstrumentSerializer(serializers.ModelSerializer):
    """Serializer simple sans méthodes complexes"""
    has_predictions = serializers.ReadOnlyField()
    
    class Meta:
        model = FinancialInstrument
        fields = ['symbol', 'label', 'sector', 'data_file', 'predictions_file', 
                 'is_active', 'market_cap', 'has_predictions']


class SimpleInstrumentPriceSerializer(serializers.ModelSerializer):
    symbol = serializers.CharField(source='instrument.symbol', read_only=True)
    
    class Meta:
        model = InstrumentPrice
        fields = ['date', 'symbol', 'open_price', 'high_price', 'low_price', 
                 'close_price', 'current_price', 'volume']


class SimpleMASIIndexSerializer(serializers.ModelSerializer):
    class Meta:
        model = MASIIndex
        fields = ['symbol', 'label', 'type', 'description', 'data_file', 'is_active']


class SimpleMASIIndexValueSerializer(serializers.ModelSerializer):
    index_symbol = serializers.CharField(source='index.symbol', read_only=True)
    
    class Meta:
        model = MASIIndexValue
        fields = ['date', 'index_symbol', 'value', 'previous_value', 
                 'daily_change', 'daily_change_pct']
