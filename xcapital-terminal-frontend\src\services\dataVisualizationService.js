import axios from 'axios';

/**
 * Data Visualization Service
 * Handles API calls for market data visualization
 */
class DataVisualizationService {
    constructor() {
        this.baseUrl = process.env.REACT_APP_DATA_VIZ_API_URL || 'http://localhost:8000/api/v1';
        this.dataPath = '/data_visualisation';
    }

    /**
     * Load CSV data from the data_visualisation directory
     */
    async loadCSVData(fileName) {
        try {
            // Load CSV file directly from public folder or via file system
            const response = await fetch(`/data_visualisation/csv/${fileName}.csv`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const csvText = await response.text();
            return this.parseCSV(csvText);
        } catch (error) {
            console.error(`Error loading CSV file ${fileName}:`, error);
            throw new Error(`Failed to load ${fileName} data`);
        }
    }

    /**
     * Load JSON data from the data_visualisation directory
     */
    async loadJSONData(fileName) {
        try {
            const response = await fetch(`/data_visualisation/json/${fileName}.json`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error(`Error loading JSON file ${fileName}:`, error);
            throw new Error(`Failed to load ${fileName} data`);
        }
    }

    /**
     * Load daily market data (indices, hausses, baisses)
     */
    async loadDailyData(date = null) {
        // Use the available date (August 19, 2025) since that's what we have in the data files
        const targetDate = date || '20250819'; // Use available date instead of today's date
        
        try {
            const promises = [
                fetch(`/data_visualisation/daily_data/indices_masi_${targetDate}.csv`).then(r => r.ok ? r.text() : null),
                fetch(`/data_visualisation/daily_data/hausses_${targetDate}.csv`).then(r => r.ok ? r.text() : null),
                fetch(`/data_visualisation/daily_data/baisses_${targetDate}.csv`).then(r => r.ok ? r.text() : null)
            ];

            const [indicesText, haussesText, baissesText] = await Promise.all(promises);

            return {
                indices: indicesText ? this.parseCSV(indicesText) : [],
                hausses: haussesText ? this.parseCSV(haussesText) : [],
                baisses: baissesText ? this.parseCSV(baissesText) : []
            };
        } catch (error) {
            console.error('Error loading daily data:', error);
            throw new Error('Failed to load daily market data');
        }
    }

    /**
     * Load scraping summary
     */
    async loadScrapingSummary(date = null) {
        // Use the available date (August 19, 2025) since that's what we have in the data files
        const targetDate = date || '20250819'; // Use available date instead of today's date
        
        try {
            const response = await axios.get(`${this.dataPath}/scraping_summary_${targetDate}.json`);
            return response.data;
        } catch (error) {
            console.error('Error loading scraping summary:', error);
            return null;
        }
    }

    /**
     * Get available indices
     */
    getAvailableIndices() {
        return [
            'MASI',
            'MASI_20',
            'MASI_ESG'
        ];
    }

    /**
     * Get available sectors
     */
    getAvailableSectors() {
        return [
            'MASI_AGROALIMENTAIRE',
            'MASI_AUTOMOBILE', 
            'MASI_BOISSONS',
            'MASI_CHIMIE',
            'MASI_EQUIPEMENT_ELECTRONIQUE_ET_ELECTRIQUE',
            'MASI_IMMOBILIER',
            'MASI_LOISIRS_ET_HOTELS',
            'MASI_MATERIELS_ET_LOGICIELS_INFORMATIQUES',
            'MASI_NTI',
            'MASI_PARTICIPATION_ET_PROMOTION_IMMOBILIERES',
            'MASI_PETROLE_ET_GAZ',
            'MASI_SANTE',
            'MASI_SERVICES_DE_TRANSPORT',
            'MASI_SOCIETES_DE_PORTEFEUILLE_HOLDINGS',
            'MASI_SOCIETE_DE_FINANCEMENT_ET_AUTRES_ACTIVITES_FINANCIERES',
            'MASI_SYLVICULTURE_ET_PAPIER',
            'MASI_TELECOMMUNICATIONS',
            'MASI_TEXTILE'
        ];
    }

    /**
     * Parse CSV text into array of objects
     */
    parseCSV(csvText) {
        const lines = csvText.trim().split('\n');
        if (lines.length < 2) return [];
        
        const headers = lines[0].split(',').map(h => h.trim());
        
        return lines.slice(1).map(line => {
            const values = line.split(',');
            const obj = {};
            headers.forEach((header, index) => {
                obj[header] = values[index]?.trim() || '';
            });
            return obj;
        }).filter(row => Object.values(row).some(val => val !== ''));
    }

    /**
     * Format sector name for display
     */
    formatSectorName(sectorCode) {
        return sectorCode
            .replace('MASI_', '')
            .replace(/_/g, ' ')
            .toLowerCase()
            .replace(/\\b\\w/g, l => l.toUpperCase());
    }

    /**
     * Calculate price change percentage
     */
    calculateChangePercentage(current, previous) {
        if (!previous || previous === 0) return 0;
        return ((current - previous) / previous) * 100;
    }

    /**
     * Generate mock data for development/demo purposes
     */
    generateMockData() {
        const today = new Date();
        const mockIndexData = [];
        
        // Generate 30 days of mock data
        for (let i = 29; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            
            const baseValue = 19500;
            const randomChange = (Math.random() - 0.5) * 200;
            const value = baseValue + randomChange + (i * 2); // Slight upward trend
            
            mockIndexData.push({
                date: date.toISOString().split('T')[0],
                value: value.toFixed(2),
                index_id: '512312',
                index_name: 'MASI',
                previous_value: i === 29 ? '' : (value - randomChange).toFixed(2),
                daily_change: i === 29 ? '' : randomChange.toFixed(2),
                daily_change_pct: i === 29 ? '' : (randomChange / baseValue * 100).toFixed(4)
            });
        }

        const mockDailyData = {
            indices: [
                { 
                    libelle: 'MASI', 
                    valeur_actuelle: '19619.56', 
                    variation_veille: '-0.64', 
                    variation_annuelle: '32.81',
                    capitalisation: '284827387097.62'
                },
                { 
                    libelle: 'MASI 20', 
                    valeur_actuelle: '1603.65', 
                    variation_veille: '-0.67', 
                    variation_annuelle: '34.42',
                    capitalisation: '226470248494.44'
                },
                { 
                    libelle: 'MASI ESG', 
                    valeur_actuelle: '1341.01', 
                    variation_veille: '-0.61', 
                    variation_annuelle: '29.60',
                    capitalisation: '30478878102.49'
                }
            ],
            hausses: [
                { libelle: 'ENNAKL', variation_rel: '9.99', volume_echange: '2309454.56', secteur: 'Distributeurs' },
                { libelle: 'S.M MONETIQUE', variation_rel: '6.02', volume_echange: '5017928.00', secteur: 'Services Informatiques' },
                { libelle: 'BALIMA', variation_rel: '6.00', volume_echange: '254.50', secteur: 'Immobilier' },
                { libelle: 'REBAB COMPANY', variation_rel: '5.99', volume_echange: '192.44', secteur: 'Mines' },
                { libelle: 'MINIERE TOUISSIT', variation_rel: '4.86', volume_echange: '424888.00', secteur: 'Mines' }
            ],
            baisses: [
                { libelle: 'STOCK A', variation_rel: '-5.25', volume_echange: '1500000.00', secteur: 'Banking' },
                { libelle: 'STOCK B', variation_rel: '-3.80', volume_echange: '2200000.00', secteur: 'Telecom' },
                { libelle: 'STOCK C', variation_rel: '-2.90', volume_echange: '800000.00', secteur: 'Energy' }
            ]
        };

        return {
            indexData: mockIndexData,
            dailyData: mockDailyData
        };
    }

    /**
     * Export data to CSV format
     */
    exportToCSV(data, filename) {
        if (!data || data.length === 0) return;
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => row[header] || '').join(','))
        ].join('\\n');
        
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `${filename}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }
}

// Export singleton instance
const dataVisualizationService = new DataVisualizationService();
export default dataVisualizationService;
