import React, { useState, useEffect, useCallback } from 'react';
import {
    Box,
    Text,
    VStack,
    HStack,
    Badge,
    Card,
    CardBody,
    Spinner,
    Alert,
    AlertIcon,
    Select,
    Stat,
    StatLabel,
    StatNumber,
    StatHelpText,
    StatArrow,
    Tabs,
    TabList,
    TabPanels,
    Tab,
    TabPanel,
    SimpleGrid
} from '@chakra-ui/react';
import { Line, Bar } from 'react-chartjs-2';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    BarElement,
    ArcElement,
    Title,
    Tooltip,
    Legend,
    Filler
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    BarElement,
    ArcElement,
    Title,
    Tooltip,
    Legend,
    Filler
);

// All available MASI indices
const masiIndices = [
    { key: 'MASI', name: 'MASI', color: '#00ff88', category: 'Main' },
    { key: 'MASI_20', name: 'MASI 20', color: '#ffce30', category: 'Main' },
    { key: 'MASI_AGROALIMENTAIRE', name: 'Agroalimentaire', color: '#ff6b6b', category: 'Sector' },
    { key: 'MASI_AUTOMOBILE', name: 'Automobile', color: '#4ecdc4', category: 'Sector' },
    { key: 'MASI_BOISSONS', name: 'Boissons', color: '#45b7d1', category: 'Sector' },
    { key: 'MASI_CHIMIE', name: 'Chimie', color: '#f39c12', category: 'Sector' },
    { key: 'MASI_EQUIPEMENT_ELECTRONIQUE_ET_ELECTRIQUE', name: 'Équipement Électronique', color: '#9b59b6', category: 'Sector' },
    { key: 'MASI_IMMOBILIER', name: 'Immobilier', color: '#e74c3c', category: 'Sector' },
    { key: 'MASI_LOISIRS_ET_HOTELS', name: 'Loisirs & Hôtels', color: '#16a085', category: 'Sector' },
    { key: 'MASI_MATERIELS_ET_LOGICIELS_INFORMATIQUES', name: 'IT & Software', color: '#3498db', category: 'Sector' },
    { key: 'MASI_NTI', name: 'NTI', color: '#2ecc71', category: 'Sector' },
    { key: 'MASI_PARTICIPATION_ET_PROMOTION_IMMOBILIERES', name: 'Promotion Immobilière', color: '#f1c40f', category: 'Sector' },
    { key: 'MASI_PETROLE_ET_GAZ', name: 'Pétrole & Gaz', color: '#34495e', category: 'Sector' },
    { key: 'MASI_SANTE', name: 'Santé', color: '#e67e22', category: 'Sector' },
    { key: 'MASI_SERVICES_DE_TRANSPORT', name: 'Transport', color: '#95a5a6', category: 'Sector' },
    { key: 'MASI_SOCIETES_DE_PORTEFEUILLE_HOLDINGS', name: 'Holdings', color: '#8e44ad', category: 'Sector' },
    { key: 'MASI_SOCIETE_DE_FINANCEMENT_ET_AUTRES_ACTIVITES_FINANCIERES', name: 'Services Financiers', color: '#27ae60', category: 'Sector' },
    { key: 'MASI_SYLVICULTURE_ET_PAPIER', name: 'Sylviculture & Papier', color: '#d35400', category: 'Sector' },
    { key: 'MASI_TELECOMMUNICATIONS', name: 'Télécommunications', color: '#7f8c8d', category: 'Sector' },
    { key: 'MASI_TEXTILE', name: 'Textile', color: '#c0392b', category: 'Sector' }
];

const MasiIndicesVisualization = () => {
    const [masiData, setMasiData] = useState({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [selectedIndices, setSelectedIndices] = useState(['MASI', 'MASI_20']);

    const parseCSV = (csvText) => {
        const lines = csvText.trim().split('\n');
        const headers = lines[0].split(',').map(h => h.trim());
        const data = [];
        
        for (let i = 1; i < lines.length; i++) {
            const values = lines[i].split(',');
            const row = {};
            headers.forEach((header, index) => {
                row[header] = values[index]?.trim();
            });
            data.push(row);
        }
        return data;
    };

    const loadMasiData = useCallback(async () => {
        setLoading(true);
        setError(null);
        
        try {
            console.log('Loading MASI data for', masiIndices.length, 'indices');
            const promises = masiIndices.map(async (index) => {
                try {
                    console.log(`Loading ${index.key}...`);
                    const response = await fetch(`/data_visualisation/csv/${index.key}.csv`);
                    if (!response.ok) throw new Error(`Failed to load ${index.key}: ${response.status}`);
                    const csvText = await response.text();
                    const data = parseCSV(csvText);
                    console.log(`Loaded ${index.key}: ${data.length} rows`);
                    return { key: index.key, name: index.name, color: index.color, category: index.category, data };
                } catch (error) {
                    console.warn(`Failed to load ${index.key}:`, error);
                    return { key: index.key, name: index.name, color: index.color, category: index.category, data: [] };
                }
            });

            const results = await Promise.all(promises);
            const dataMap = {};
            results.forEach(result => {
                dataMap[result.key] = result;
            });
            
            console.log('MASI data loaded:', Object.keys(dataMap).length, 'indices');
            console.log('Sample data structure:', dataMap['MASI']?.data?.[0]);
            setMasiData(dataMap);
        } catch (error) {
            console.error('Error loading MASI data:', error);
            setError('Failed to load MASI data');
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        loadMasiData();
    }, [loadMasiData]);

    const getLatestValue = (data) => {
        if (!data || data.length === 0) return null;
        const latest = data[data.length - 1];
        return {
            value: parseFloat(latest.value || latest.Close || latest.Price || latest.Value || 0),
            date: latest.date || latest.Date || latest.Timestamp || 'N/A',
            change: parseFloat(latest.daily_change || latest.Change || 0),
            changePercent: parseFloat(latest.daily_change_pct || latest['Change%'] || latest.ChangePercent || 0)
        };
    };

    const createLineChartData = () => {
        console.log('Creating chart data for indices:', selectedIndices);
        console.log('Available MASI data keys:', Object.keys(masiData));
        
        const datasets = selectedIndices.map(indexKey => {
            const indexData = masiData[indexKey];
            console.log(`Processing ${indexKey}:`, indexData ? `${indexData.data?.length} data points` : 'no data');
            
            if (!indexData || !indexData.data) {
                console.warn(`No data for ${indexKey}`);
                return null;
            }

            const data = indexData.data.slice(-30); // Last 30 points
            console.log(`${indexKey} chart data points:`, data.length);
            if (data.length > 0) {
                console.log(`${indexKey} sample data:`, data[0]);
            }
            
            return {
                label: indexData.name,
                data: data.map(d => parseFloat(d.value || d.Close || d.Price || d.Value || 0)),
                borderColor: indexData.color,
                backgroundColor: `${indexData.color}20`,
                fill: false,
                tension: 0.1
            };
        }).filter(Boolean);

        const labels = masiData[selectedIndices[0]]?.data?.slice(-30)?.map(d => {
            const date = new Date(d.date || d.Date || d.Timestamp);
            return date.toLocaleDateString('fr-FR', { month: 'short', day: 'numeric' });
        }) || [];

        console.log('Chart datasets:', datasets.length);
        console.log('Chart labels:', labels.length);

        return { labels, datasets };
    };

    const createSectorPerformanceData = () => {
        const sectorIndices = masiIndices.filter(idx => idx.category === 'Sector');
        const performanceData = sectorIndices.map(index => {
            const data = masiData[index.key]?.data;
            const latest = getLatestValue(data);
            return {
                name: index.name,
                performance: latest?.changePercent || 0,
                color: index.color
            };
        }).filter(sector => sector.performance !== 0)
         .sort((a, b) => b.performance - a.performance);

        return {
            labels: performanceData.map(s => s.name),
            datasets: [{
                data: performanceData.map(s => s.performance),
                backgroundColor: performanceData.map(s => s.color),
                borderColor: performanceData.map(s => s.color),
                borderWidth: 1
            }]
        };
    };

    if (loading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" h="400px">
                <VStack>
                    <Spinner size="xl" color="#ffce30" />
                    <Text color="#ffffff">Loading MASI indices data...</Text>
                </VStack>
            </Box>
        );
    }

    if (error) {
        return (
            <Alert status="error" bg="#2a2a2a" borderRadius="md">
                <AlertIcon />
                <Text color="#ffffff">{error}</Text>
            </Alert>
        );
    }

    const mainIndices = masiIndices.filter(idx => idx.category === 'Main');
    const sectorIndices = masiIndices.filter(idx => idx.category === 'Sector');

    return (
        <Box bg="#1a1a1a" p={6} borderRadius="md" border="1px solid #333">
            <VStack spacing={6} align="stretch">
                {/* Header */}
                <HStack justify="space-between" align="center">
                    <VStack align="start" spacing={1}>
                        <Text color="#ffffff" fontSize="24px" fontWeight="bold">
                            MASI Indices Comprehensive Analysis
                        </Text>
                        <Text color="#888" fontSize="14px">
                            Real-time market indices performance & sector analysis
                        </Text>
                    </VStack>
                    <Badge colorScheme="green" fontSize="12px" px={3} py={1}>
                        {Object.keys(masiData).length} Indices Loaded
                    </Badge>
                </HStack>

                {/* Main Indices Overview */}
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                    {mainIndices.map(index => {
                        const data = masiData[index.key]?.data;
                        const latest = getLatestValue(data);
                        return (
                            <Card key={index.key} bg="#2a2a2a" borderColor="#444">
                                <CardBody>
                                    <Stat>
                                        <StatLabel color="#888">{index.name}</StatLabel>
                                        <StatNumber color="#ffffff">
                                            {latest?.value?.toFixed(2) || 'N/A'}
                                        </StatNumber>
                                        <StatHelpText color="#888">
                                            <StatArrow type={latest?.changePercent >= 0 ? 'increase' : 'decrease'} />
                                            {latest?.changePercent?.toFixed(2)}% ({latest?.change?.toFixed(2)})
                                        </StatHelpText>
                                    </Stat>
                                </CardBody>
                            </Card>
                        );
                    })}
                </SimpleGrid>

                {/* Tabs for different views */}
                <Tabs variant="enclosed" colorScheme="yellow">
                    <TabList bg="#2a2a2a" borderRadius="md">
                        <Tab color="#888" _selected={{ color: "#ffffff", bg: "#333" }}>
                            Performance Trends
                        </Tab>
                        <Tab color="#888" _selected={{ color: "#ffffff", bg: "#333" }}>
                            Sector Comparison
                        </Tab>
                        <Tab color="#888" _selected={{ color: "#ffffff", bg: "#333" }}>
                            Sector Performance
                        </Tab>
                    </TabList>

                    <TabPanels>
                        {/* Performance Trends */}
                        <TabPanel>
                            <VStack spacing={4} align="stretch">
                                <HStack>
                                    <Text color="#ffffff" fontSize="16px" fontWeight="bold">
                                        Select Indices to Compare:
                                    </Text>
                                    <Select
                                        placeholder="Add index..."
                                        bg="#2a2a2a"
                                        border="1px solid #444"
                                        color="#ffffff"
                                        onChange={(e) => {
                                            if (e.target.value && !selectedIndices.includes(e.target.value)) {
                                                setSelectedIndices([...selectedIndices, e.target.value]);
                                            }
                                        }}
                                        maxW="300px"
                                    >
                                        {masiIndices.map(index => (
                                            <option key={index.key} value={index.key} style={{ background: "#2a2a2a" }}>
                                                {index.name}
                                            </option>
                                        ))}
                                    </Select>
                                </HStack>

                                <HStack wrap="wrap">
                                    {selectedIndices.map(indexKey => {
                                        const index = masiIndices.find(idx => idx.key === indexKey);
                                        return (
                                            <Badge
                                                key={indexKey}
                                                colorScheme="yellow"
                                                cursor="pointer"
                                                onClick={() => setSelectedIndices(selectedIndices.filter(id => id !== indexKey))}
                                            >
                                                {index?.name} ✕
                                            </Badge>
                                        );
                                    })}
                                </HStack>

                                <Box bg="#2a2a2a" p={4} borderRadius="md" border="1px solid #444">
                                    <div style={{ height: '400px' }}>
                                        <Line 
                                            data={createLineChartData()}
                                            options={{
                                                responsive: true,
                                                maintainAspectRatio: false,
                                                scales: {
                                                    x: {
                                                        grid: { color: '#333' },
                                                        ticks: { color: '#888', font: { size: 11 } }
                                                    },
                                                    y: {
                                                        grid: { color: '#333' },
                                                        ticks: { color: '#888', font: { size: 11 } }
                                                    }
                                                },
                                                plugins: {
                                                    legend: { labels: { color: '#ffffff', font: { size: 12 } } },
                                                    tooltip: {
                                                        backgroundColor: '#1a1a1a',
                                                        titleColor: '#ffffff',
                                                        bodyColor: '#ffffff',
                                                        borderColor: '#333',
                                                        borderWidth: 1
                                                    }
                                                }
                                            }}
                                        />
                                    </div>
                                </Box>
                            </VStack>
                        </TabPanel>

                        {/* Sector Comparison */}
                        <TabPanel>
                            <SimpleGrid columns={{ base: 2, md: 3, lg: 4 }} spacing={4}>
                                {sectorIndices.map(index => {
                                    const data = masiData[index.key]?.data;
                                    const latest = getLatestValue(data);
                                    return (
                                        <Card key={index.key} bg="#2a2a2a" borderColor="#444" size="sm">
                                            <CardBody>
                                                <VStack spacing={2}>
                                                    <Text color="#ffffff" fontSize="12px" fontWeight="bold" textAlign="center">
                                                        {index.name}
                                                    </Text>
                                                    <Text color={index.color} fontSize="16px" fontWeight="bold">
                                                        {latest?.value?.toFixed(2) || 'N/A'}
                                                    </Text>
                                                    <HStack>
                                                        <Text 
                                                            color={latest?.changePercent >= 0 ? '#00ff88' : '#ff6b6b'} 
                                                            fontSize="12px"
                                                        >
                                                            {latest?.changePercent >= 0 ? '▲' : '▼'} {latest?.changePercent?.toFixed(2)}%
                                                        </Text>
                                                    </HStack>
                                                </VStack>
                                            </CardBody>
                                        </Card>
                                    );
                                })}
                            </SimpleGrid>
                        </TabPanel>

                        {/* Sector Performance */}
                        <TabPanel>
                            <Box bg="#2a2a2a" p={4} borderRadius="md" border="1px solid #444">
                                <Text color="#ffffff" fontSize="16px" fontWeight="bold" mb={4}>
                                    Sector Performance Ranking
                                </Text>
                                <div style={{ height: '400px' }}>
                                    <Bar 
                                        data={createSectorPerformanceData()}
                                        options={{
                                            responsive: true,
                                            maintainAspectRatio: false,
                                            indexAxis: 'y',
                                            scales: {
                                                x: {
                                                    grid: { color: '#333' },
                                                    ticks: { 
                                                        color: '#888', 
                                                        font: { size: 11 },
                                                        callback: function(value) {
                                                            return value + '%';
                                                        }
                                                    }
                                                },
                                                y: {
                                                    grid: { color: '#333' },
                                                    ticks: { 
                                                        color: '#888', 
                                                        font: { size: 10 },
                                                        maxRotation: 0
                                                    }
                                                }
                                            },
                                            plugins: {
                                                legend: { display: false },
                                                tooltip: {
                                                    backgroundColor: '#1a1a1a',
                                                    titleColor: '#ffffff',
                                                    bodyColor: '#ffffff',
                                                    borderColor: '#333',
                                                    borderWidth: 1,
                                                    callbacks: {
                                                        label: function(context) {
                                                            return `Performance: ${context.parsed.x.toFixed(2)}%`;
                                                        }
                                                    }
                                                }
                                            }
                                        }}
                                    />
                                </div>
                            </Box>
                        </TabPanel>
                    </TabPanels>
                </Tabs>
            </VStack>
        </Box>
    );
};

export default MasiIndicesVisualization;
