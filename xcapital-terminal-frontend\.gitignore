# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

#for the backend 
xcapitalbackend/.env

# Large data files and models
/data/*.csv
/models/*.pkl
/models/*.joblib
/models/*.h5
/models/*.pt
/models/*.pth
/models/*.onnx
/public/data_visualisation/*.csv
/public/updated_predictions/*.csv

# Large directories
data_visualisation/
public/data_visualisation/
public/updated_predictions/

# Python bytecode and cache
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# API and backend files (if separate)
api/__pycache__/
api/*.pyc
api/logs/
api/temp/

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Large files (> 100MB)
*.zip
*.tar.gz
*.rar
*.7z
*.dmg
*.iso

# Log files
*.log
logs/
log/
