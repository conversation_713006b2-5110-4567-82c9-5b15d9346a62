# Generated by Django 5.2.5 on 2025-08-20 19:38

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MarketIndex',
            fields=[
                ('code', models.CharField(max_length=20, primary_key=True, serialize=False, unique=True)),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('currency', models.CharField(default='MAD', max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'market_indices',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='Stock',
            fields=[
                ('ticker', models.Char<PERSON>ield(max_length=20, primary_key=True, serialize=False, unique=True)),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('sector', models.CharField(blank=True, max_length=100, null=True)),
                ('market_cap', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('currency', models.CharField(default='MAD', max_length=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'stocks',
                'ordering': ['ticker'],
            },
        ),
        migrations.CreateModel(
            name='TradingSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(unique=True)),
                ('is_trading_day', models.BooleanField(default=True)),
                ('session_start', models.TimeField(blank=True, null=True)),
                ('session_end', models.TimeField(blank=True, null=True)),
                ('total_volume', models.BigIntegerField(default=0)),
                ('total_value', models.DecimalField(decimal_places=2, default=0, max_digits=20)),
                ('notes', models.TextField(blank=True)),
            ],
            options={
                'db_table': 'trading_sessions',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='IndexPrice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('open_value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('high_value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('low_value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('close_value', models.DecimalField(decimal_places=4, max_digits=15)),
                ('volume', models.BigIntegerField(default=0)),
                ('index', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prices', to='market_data.marketindex')),
            ],
            options={
                'db_table': 'index_prices',
                'ordering': ['-date'],
                'indexes': [models.Index(fields=['index', 'date'], name='index_price_index_i_ce60ea_idx'), models.Index(fields=['date'], name='index_price_date_a95aad_idx')],
                'unique_together': {('index', 'date')},
            },
        ),
        migrations.CreateModel(
            name='StockPrice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('open_price', models.DecimalField(decimal_places=4, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.0001'))])),
                ('high_price', models.DecimalField(decimal_places=4, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.0001'))])),
                ('low_price', models.DecimalField(decimal_places=4, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.0001'))])),
                ('close_price', models.DecimalField(decimal_places=4, max_digits=15, validators=[django.core.validators.MinValueValidator(Decimal('0.0001'))])),
                ('volume', models.BigIntegerField(validators=[django.core.validators.MinValueValidator(0)])),
                ('adj_close', models.DecimalField(blank=True, decimal_places=4, max_digits=15, null=True)),
                ('stock', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prices', to='market_data.stock')),
            ],
            options={
                'db_table': 'stock_prices',
                'ordering': ['-date'],
                'indexes': [models.Index(fields=['stock', 'date'], name='stock_price_stock_i_b1c210_idx'), models.Index(fields=['date'], name='stock_price_date_9d4b65_idx')],
                'unique_together': {('stock', 'date')},
            },
        ),
    ]
