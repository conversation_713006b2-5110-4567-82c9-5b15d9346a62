#!/usr/bin/env python3
"""
Test script to diagnose ARIMA model loading issues
"""

import pickle
import os
from pathlib import Path

def test_pickle_file(filepath):
    """Test if a pickle file can be loaded"""
    try:
        print(f"Testing: {filepath}")
        
        # Check file size
        size = os.path.getsize(filepath)
        print(f"  File size: {size:,} bytes")
        
        # Try to read first few bytes
        with open(filepath, 'rb') as f:
            first_bytes = f.read(10)
            print(f"  First 10 bytes: {first_bytes}")
        
        # Try to load with pickle
        with open(filepath, 'rb') as f:
            try:
                model = pickle.load(f)
                print(f"  ✅ SUCCESS: Model loaded successfully")
                print(f"  Model type: {type(model)}")
                return True
            except Exception as e:
                print(f"  ❌ FAILED: {str(e)}")
                return False
                
    except Exception as e:
        print(f"  ❌ FILE ERROR: {str(e)}")
        return False

def main():
    models_dir = Path("../models/Arima")
    
    if not models_dir.exists():
        print(f"❌ Models directory not found: {models_dir}")
        return
    
    print("🔍 Testing ARIMA Model Files")
    print("=" * 50)
    
    # Test first 3 model files
    model_files = list(models_dir.glob("*.pkl"))[:3]
    
    if not model_files:
        print("❌ No pickle files found in models directory")
        return
    
    success_count = 0
    for model_file in model_files:
        if test_pickle_file(model_file):
            success_count += 1
        print()
    
    print(f"📊 Results: {success_count}/{len(model_files)} models loaded successfully")
    
    if success_count == 0:
        print("\n💡 Troubleshooting suggestions:")
        print("1. Models might be created with a different Python version")
        print("2. Models might be corrupted during transfer")
        print("3. Models might use libraries not installed")
        print("4. Try regenerating the models")

if __name__ == "__main__":
    main()
