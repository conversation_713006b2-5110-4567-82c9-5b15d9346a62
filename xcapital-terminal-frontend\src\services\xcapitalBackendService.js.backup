/**
 * XCapital Backend API Service
 * Connects to the Django backend APIs for real market data
 */

c    /**
     * Get company variations
     */
    async getCompanyVariations(symbol) {
        return this.makeRequest(`/companies/${symbol}/variations/`);
    }

    /**
     * Get comprehensive company data with period
     */
    async getCompanyData(symbol, startDate = null, endDate = null) {
        let endpoint = '/secure/company-data/';
        const params = new URLSearchParams({ symbol });
        
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        
        return this.makeRequest(`${endpoint}?${params.toString()}`);
    }

    /**
     * Get market overview data
     */ASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000';

class XCapitalBackendService {
    constructor() {
        this.baseURL = `${API_BASE_URL}/api/v1/xcapital`;
    }

    /**
     * Test backend connectivity
     */
    async testConnection() {
        try {
            const response = await fetch(this.baseURL, {
                method: 'OPTIONS',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            return response.ok;
        } catch (error) {
            console.error('Backend connection test failed:', error);
            return false;
        }
    }

    /**
     * Generic API request handler
     */
    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            credentials: 'same-origin',
        };

        try {
            const response = await fetch(url, { ...defaultOptions, ...options });
            
            if (!response.ok) {
                throw new Error(`API request failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error(`API Error for ${endpoint}:`, error);
            throw error;
        }
    }

    // =========================
    // COMPANIES APIs
    // =========================

    /**
     * Get simple companies list
     */
    async getCompanies() {
        return this.makeRequest('/companies/');
    }

    /**
     * Get full companies list with pagination
     */
    async getCompaniesFullList(page = 1, pageSize = 50) {
        return this.makeRequest(`/companies/full/?page=${page}&page_size=${pageSize}`);
    }

    /**
     * Get company details by symbol
     */
    async getCompanyDetail(symbol) {
        return this.makeRequest(`/companies/${symbol}/`);
    }

    /**
     * Get company price data
     */
    async getCompanyPrices(symbol, startDate = null, endDate = null) {
        let endpoint = `/companies/${symbol}/prices/`;
        const params = new URLSearchParams();
        
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        
        if (params.toString()) {
            endpoint += `?${params.toString()}`;
        }
        
        return this.makeRequest(endpoint);
    }

    /**
     * Get company chart data
     */
    async getCompanyChartData(symbol, period = '1M') {
        return this.makeRequest(`/companies/${symbol}/chart/?period=${period}`);
    }

    /**
     * Get company price variations
     */
    async getCompanyVariations(symbol) {
        return this.makeRequest(`/companies/${symbol}/variations/`);
    }

    /**
     * Get comprehensive company data with period
     */
    async getCompanyData(symbol, startDate = null, endDate = null) {
        let endpoint = '/secure/company-data/';
        const params = new URLSearchParams({ symbol });
        
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        
        return this.makeRequest(`${endpoint}?${params.toString()}`);
    }

    /**
     * Get market overview data
     */
    async getMarketOverview() {
        return this.makeRequest('/market/overview/');
    }

    /**
     * Get available symbols
     */
    async getAvailableSymbols() {
        return this.makeRequest('/symbols/');
    }

    /**
     * Get available companies list
     */
    async getAvailableCompanies() {
        return this.makeRequest('/available-companies/');
    }

    // =========================
    // INDICES APIs
    // =========================

    /**
     * Get simple indices list
     */
    async getIndices() {
        return this.makeRequest('/indices/');
    }

    /**
     * Get full indices list with pagination
     */
    async getIndicesFullList(page = 1, pageSize = 50) {
        return this.makeRequest(`/indices/full/?page=${page}&page_size=${pageSize}`);
    }

    /**
     * Get index details by ID
     */
    async getIndexDetail(indexId) {
        return this.makeRequest(`/indices/${indexId}/`);
    }

    /**
     * Get index values/prices
     */
    async getIndexValues(indexId, startDate = null, endDate = null) {
        let endpoint = `/indices/${indexId}/values/`;
        const params = new URLSearchParams();
        
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        
        if (params.toString()) {
            endpoint += `?${params.toString()}`;
        }
        
        return this.makeRequest(endpoint);
    }

    /**
     * Get index chart data
     */
    async getIndexChartData(indexId, period = '1M') {
        try {
            return await this.makeRequest(`/indices/${indexId}/chart/?period=${period}`);
        } catch (error) {
            console.warn(`Chart data not available for index ${indexId}:`, error.message);
            // Return mock data structure for graceful fallback
            return {
                index_id: indexId,
                index_name: indexId,
                period: period,
                data: this.generateMockChartData(period)
            };
        }
    }

    /**
     * Generate mock chart data for fallback
     */
    generateMockChartData(period = '1M') {
        const days = period === '1D' ? 1 : period === '1W' ? 7 : period === '1M' ? 30 : 90;
        const baseValue = 13000;
        const data = [];
        
        for (let i = 0; i < days; i++) {
            const date = new Date();
            date.setDate(date.getDate() - (days - 1 - i));
            
            const variance = (Math.random() - 0.5) * 200;
            const value = baseValue + variance + (i * 5);
            
            data.push({
                date: date.toISOString().split('T')[0],
                value: value,
                previous_value: value - 10,
                daily_change: variance,
                daily_change_pct: (variance / baseValue) * 100
            });
        }
        
        return data;
    }

    /**
     * Get index variations
     */
    async getIndexVariations(indexId) {
        return this.makeRequest(`/indices/${indexId}/variations/`);
    }

    /**
     * Get available indices
     */
    async getAvailableIndices() {
        return this.makeRequest('/available-indices/');
    }

    /**
     * Get detailed indices list
     */
    async getIndicesList() {
        return this.makeRequest('/indices-list/');
    }

    // =========================
    // FORM DATA APIs (POST)
    // =========================

    /**
     * Submit company data form
     */
    async submitCompanyDataForm(formData) {
        return this.makeRequest('/companies/form-data/', {
            method: 'POST',
            body: JSON.stringify(formData),
        });
    }

    /**
     * Submit index data form
     */
    async submitIndexDataForm(formData) {
        return this.makeRequest('/indices/form-data/', {
            method: 'POST',
            body: JSON.stringify(formData),
        });
    }

    /**
     * Get secure/clean company data
     */
    async getSecureCompanyData(formData) {
        return this.makeRequest('/secure/company-data/', {
            method: 'POST',
            body: JSON.stringify(formData),
        });
    }

    // =========================
    // DASHBOARD-SPECIFIC METHODS
    // =========================

    /**
     * Get dashboard data combining multiple endpoints
     */
    async getDashboardData() {
        try {
            const [marketOverview, topCompanies, indices] = await Promise.all([
                this.getMarketOverview(),
                this.getAvailableCompanies(),
                this.getAvailableIndices()
            ]);

            return {
                marketOverview,
                topCompanies: topCompanies.slice(0, 10), // Top 10 companies
                indices,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error fetching dashboard data:', error);
            throw error;
        }
    }

    /**
     * Get market trends data for charts
     */
    async getMarketTrendsData(indexId = 'MASI', period = '30D') {
        try {
            const chartData = await this.getIndexChartData(indexId, period);
            
            // Transform data for Chart.js
            if (chartData && chartData.data) {
                const transformedData = {
                    labels: chartData.data.map(item => 
                        new Date(item.date).toLocaleDateString('en-US', { 
                            month: 'short', 
                            day: 'numeric' 
                        })
                    ),
                    datasets: [{
                        label: `${indexId} Index`,
                        data: chartData.data.map(item => parseFloat(item.value || item.close || item.price)),
                        borderColor: '#ffce30',
                        backgroundColor: 'rgba(255, 206, 48, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 2,
                        pointHoverRadius: 6
                    }]
                };
                return transformedData;
            }
            
            return null;
        } catch (error) {
            console.error('Error fetching market trends:', error);
            return null;
        }
    }

    /**
     * Get top trading volumes for volume charts
     */
    async getTopTradingVolumes(limit = 7) {
        try {
            const companies = await this.getAvailableCompanies();
            
            // Filter and sort by volume if available
            const companiesWithVolume = companies
                .filter(company => company.volume && parseFloat(company.volume) > 0)
                .sort((a, b) => parseFloat(b.volume) - parseFloat(a.volume))
                .slice(0, limit);

            if (companiesWithVolume.length === 0) {
                // Fallback to market overview if no volume data
                const marketData = await this.getMarketOverview();
                return this.transformVolumeDataFromMarketOverview(marketData, limit);
            }

            // Transform for Chart.js
            return {
                labels: companiesWithVolume.map(company => 
                    (company.name || company.symbol || company.libelle || 'Unknown').substring(0, 15) + '...'
                ),
                datasets: [{
                    label: 'Trading Volume (Million MAD)',
                    data: companiesWithVolume.map(company => 
                        parseFloat(company.volume) / 1000000 // Convert to millions
                    ),
                    backgroundColor: [
                        '#ffce30', '#4ade80', '#f87171', '#60a5fa', 
                        '#a78bfa', '#fb7185', '#34d399'
                    ],
                    borderColor: '#333',
                    borderWidth: 1
                }]
            };
        } catch (error) {
            console.error('Error fetching trading volumes:', error);
            return null;
        }
    }

    /**
     * Transform market overview data for volume chart
     */
    transformVolumeDataFromMarketOverview(marketData, limit = 7) {
        if (!marketData || !marketData.top_movers) {
            return null;
        }

        const topMovers = marketData.top_movers.slice(0, limit);
        
        return {
            labels: topMovers.map(item => 
                (item.name || item.symbol || 'Unknown').substring(0, 15) + '...'
            ),
            datasets: [{
                label: 'Price Change (%)',
                data: topMovers.map(item => 
                    Math.abs(parseFloat(item.change_percent || item.variation || 0))
                ),
                backgroundColor: [
                    '#ffce30', '#4ade80', '#f87171', '#60a5fa', 
                    '#a78bfa', '#fb7185', '#34d399'
                ],
                borderColor: '#333',
                borderWidth: 1
            }]
        };
    }

    /**
     * Get daily market statistics
     */
    async getDailyMarketStats() {
        try {
            const [marketOverview, indices] = await Promise.all([
                this.getMarketOverview(),
                this.getAvailableIndices()
            ]);

            console.log('Raw backend data:', { marketOverview, indices });

            // Extract hausses (gains) and baisses (losses) from market data
            const hausses = [];
            const baisses = [];

            if (marketOverview && marketOverview.gainers && Array.isArray(marketOverview.gainers)) {
                hausses.push(...marketOverview.gainers);
            }
            if (marketOverview && marketOverview.losers && Array.isArray(marketOverview.losers)) {
                baisses.push(...marketOverview.losers);
            }

            return {
                indices: Array.isArray(indices) ? indices.slice(0, 10) : [], // Top 10 indices (safe array check)
                hausses,
                baisses,
                summary: {
                    total_companies: marketOverview.total_companies || 0,
                    gainers_count: hausses.length,
                    losers_count: baisses.length,
                    unchanged: (marketOverview.total_companies || 0) - hausses.length - baisses.length
                }
            };
        } catch (error) {
            console.error('Error fetching daily market stats:', error);
            throw error;
        }
    }

    // =========================
    // COMPREHENSIVE DASHBOARD METHODS
    // =========================

    /**
     * Get Market Performance Trend Data
     * Combines multiple indices to show overall market performance
     */
    async getMarketPerformanceTrend(period = '30D') {
        try {
            // Get major indices for performance trend
            const majorIndices = ['MASI', 'MADEX', 'MSI20']; // Major Morocco indices
            
            const performanceData = await Promise.all(
                majorIndices.map(async (indexId) => {
                    try {
                        const chartData = await this.getIndexChartData(indexId, period);
                        return { indexId, data: chartData };
                    } catch (error) {
                        console.warn(`Failed to get data for ${indexId}:`, error);
                        return { indexId, data: null };
                    }
                })
            );

            // Transform for Chart.js
            const combinedData = {
                labels: [],
                datasets: []
            };

            performanceData.forEach(({ indexId, data }, index) => {
                if (data && data.data && data.data.length > 0) {
                    if (combinedData.labels.length === 0) {
                        combinedData.labels = data.data.map(item => 
                            new Date(item.date).toLocaleDateString('en-US', { 
                                month: 'short', 
                                day: 'numeric' 
                            })
                        );
                    }

                    const colors = ['#ffce30', '#4ade80', '#f87171'];
                    combinedData.datasets.push({
                        label: `${indexId} Index`,
                        data: data.data.map(item => parseFloat(item.value || item.close || item.price)),
                        borderColor: colors[index] || '#60a5fa',
                        backgroundColor: `${colors[index] || '#60a5fa'}20`,
                        borderWidth: 2,
                        fill: false,
                        tension: 0.4,
                        pointRadius: 2,
                        pointHoverRadius: 6
                    });
                }
            });

            return combinedData;
        } catch (error) {
            console.error('Error fetching market performance trend:', error);
            return null;
        }
    }

    /**
     * Get Trading Volume Analysis across all companies
     */
    async getTradingVolumeAnalysis(limit = 10) {
        try {
            const marketOverview = await this.getMarketOverview();
            
            // Get top companies by volume from market overview
            let volumeData = [];
            
            if (marketOverview.top_companies_by_volume) {
                volumeData = marketOverview.top_companies_by_volume.slice(0, limit);
            } else {
                // Fallback: get companies and try to get their volume data
                const companies = await this.getAvailableCompanies();
                volumeData = companies
                    .filter(company => company.volume && parseFloat(company.volume) > 0)
                    .sort((a, b) => parseFloat(b.volume) - parseFloat(a.volume))
                    .slice(0, limit);
            }

            // Transform for Chart.js
            return {
                labels: volumeData.map(item => 
                    (item.name || item.symbol || item.libelle || 'Unknown').substring(0, 20)
                ),
                datasets: [{
                    label: 'Trading Volume',
                    data: volumeData.map(item => 
                        parseFloat(item.recent_volume || item.volume || 0)
                    ),
                    backgroundColor: [
                        '#ffce30', '#4ade80', '#f87171', '#60a5fa', '#a78bfa',
                        '#fb7185', '#34d399', '#fbbf24', '#f472b6', '#8b5cf6'
                    ],
                    borderColor: '#333',
                    borderWidth: 1
                }]
            };
        } catch (error) {
            console.error('Error fetching trading volume analysis:', error);
            return null;
        }
    }

    /**
     * Get Top Gainers Today using price variations
     */
    async getTopGainersToday(limit = 10) {
        try {
            const companies = await this.getAvailableCompanies();

            // Get price variations for each company
            const variationPromises = companies.slice(0, 20).map(async (company) => {
                try {
                    const variations = await this.getCompanyVariations(company.symbol);
                    if (variations && variations.variations && variations.variations.length > 0) {
                        const latestVariation = variations.variations[variations.variations.length - 1];
                        const changePercent = parseFloat(latestVariation.price_change_percent || 0);
                        
                        if (changePercent > 0) {
                            return {
                                symbol: company.symbol,
                                name: company.nom_francais || company.name || company.symbol,
                                change_percent: changePercent,
                                current_price: parseFloat(latestVariation.prix_fermeture || 0),
                                volume: parseFloat(latestVariation.volume || 0)
                            };
                        }
                    }
                    return null;
                } catch (error) {
                    console.warn(`Failed to get variations for ${company.symbol}:`, error);
                    return null;
                }
            });

            const results = await Promise.all(variationPromises);
            const gainers = results
                .filter(item => item !== null)
                .sort((a, b) => b.change_percent - a.change_percent)
                .slice(0, limit);

            return gainers;
        } catch (error) {
            console.error('Error fetching top gainers:', error);
            return [];
        }
    }

    /**
     * Get Top Losers Today using price variations
     */
    async getTopLosersToday(limit = 10) {
        try {
            const companies = await this.getAvailableCompanies();

            // Get price variations for each company
            const variationPromises = companies.slice(0, 20).map(async (company) => {
                try {
                    const variations = await this.getCompanyVariations(company.symbol);
                    if (variations && variations.variations && variations.variations.length > 0) {
                        const latestVariation = variations.variations[variations.variations.length - 1];
                        const changePercent = parseFloat(latestVariation.price_change_percent || 0);
                        
                        if (changePercent < 0) {
                            return {
                                symbol: company.symbol,
                                name: company.nom_francais || company.name || company.symbol,
                                change_percent: changePercent,
                                current_price: parseFloat(latestVariation.prix_fermeture || 0),
                                volume: parseFloat(latestVariation.volume || 0)
                            };
                        }
                    }
                    return null;
                } catch (error) {
                    console.warn(`Failed to get variations for ${company.symbol}:`, error);
                    return null;
                }
            });

            const results = await Promise.all(variationPromises);
            const losers = results
                .filter(item => item !== null)
                .sort((a, b) => a.change_percent - b.change_percent)
                .slice(0, limit);

            return losers;
        } catch (error) {
            console.error('Error fetching top losers:', error);
            return [];
        }
    }

    /**
     * Get comprehensive dashboard data with all visualizations
     */
    async getComprehensiveDashboardData() {
        try {
            console.log('Fetching comprehensive dashboard data...');
            
            const [
                marketOverview,
                performanceTrend,
                volumeAnalysis,
                topGainers,
                topLosers,
                indices,
                companies
            ] = await Promise.all([
                this.getMarketOverview(),
                this.getMarketPerformanceTrend(),
                this.getTradingVolumeAnalysis(),
                this.getTopGainersToday(),
                this.getTopLosersToday(),
                this.getAvailableIndices(),
                this.getAvailableCompanies()
            ]);

            return {
                marketOverview,
                performanceTrend,
                volumeAnalysis,
                topGainers,
                topLosers,
                indices: Array.isArray(indices) ? indices.slice(0, 10) : [],
                companies: Array.isArray(companies) ? companies.slice(0, 20) : [],
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('Error fetching comprehensive dashboard data:', error);
            throw error;
        }
    }
}

// Export singleton instance
const xcapitalBackendService = new XCapitalBackendService();
export default xcapitalBackendService;
