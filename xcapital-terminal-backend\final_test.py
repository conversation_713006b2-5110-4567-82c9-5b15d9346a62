import requests
import json

def test_api():
    url = "http://127.0.0.1:8000/api/v1/xcapital/indices/form-data/"
    
    # Votre requête originale qui ne fonctionnait pas
    data = {
        "indices": "512336",
        "period": "CUSTOM",
        "start_date": "2024-08-20",
        "end_date": "2024-08-30"
    }
    
    print("Test de l'API avec votre requête originale...")
    print(f"URL: {url}")
    print(f"Data: {data}")
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"\nStatus Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Succès!")
            if 'summary' in result:
                summary = result['summary']
                print(f"Index: {summary.get('index_name')} ({summary.get('index_id')})")
                print(f"Période: {summary.get('start_date')} à {summary.get('end_date')}")
                print(f"Points de données: {summary.get('data_points')}")
                print(f"Valeur initiale: {summary.get('first_value')}")
                print(f"Valeur finale: {summary.get('last_value')}")
            else:
                print("Réponse sans summary:", result)
        else:
            print("❌ Erreur:")
            try:
                error_data = response.json()
                print(json.dumps(error_data, indent=2, ensure_ascii=False))
            except:
                print(response.text)
                
    except requests.exceptions.ConnectionError:
        print("❌ Erreur de connexion - Assurez-vous que le serveur Django tourne sur http://127.0.0.1:8000")
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    test_api()
