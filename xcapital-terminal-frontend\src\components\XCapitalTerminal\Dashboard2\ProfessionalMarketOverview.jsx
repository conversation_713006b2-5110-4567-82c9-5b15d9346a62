import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
    Box,
    VStack,
    Text,
    Heading,
    Card,
    CardBody,
    Badge,
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    TableContainer,
    IconButton,
    useToast,
    Skeleton,
    Container,
    SimpleGrid,
    ButtonGroup,
    Select,
    Stat,
    StatLabel,
    StatNumber,
    StatHelpText,
    StatArrow,
    Flex,
    Spinner,
    Icon,
    Divider
} from '@chakra-ui/react';
import { FiBarChart2, FiTrendingUp, FiTrendingDown, FiRefreshCw, FiPieChart, FiDollarSign, FiActivity, FiBarChart } from 'react-icons/fi';
import {
    ResponsiveContainer,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    LineChart,
    Line,
    AreaChart,
    Area
} from 'recharts';
import { motion } from 'framer-motion';
import xcapitalBackendService from '../../../services/xcapitalBackendService';

const MotionCard = motion(Card);
const MotionBox = motion(Box);

// Helper function to safely handle market sentiment data
const getMarketSentimentDisplay = (marketSentiment) => {
    // If it's already a string (legacy format), return it
    if (typeof marketSentiment === 'string') {
        return marketSentiment;
    }

    // If it's an object with sentiment data, calculate sentiment
    if (typeof marketSentiment === 'object' && marketSentiment !== null) {
        const { positive_percentage, negative_percentage } = marketSentiment;

        if (positive_percentage !== undefined && negative_percentage !== undefined) {
            if (positive_percentage > 60) return 'Bullish';
            if (negative_percentage > 60) return 'Bearish';
            return 'Neutral';
        }
    }

    return 'Neutral';
};

const getMarketSentimentColor = (marketSentiment) => {
    const sentiment = getMarketSentimentDisplay(marketSentiment);
    switch (sentiment) {
        case 'Bullish': return 'green.400';
        case 'Bearish': return 'red.400';
        default: return 'yellow.400';
    }
};

const ProfessionalMarketOverview = () => {
    // States for API data only
    const [isLoading, setIsLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [indicesData, setIndicesData] = useState([]);
    const [companiesData, setCompaniesData] = useState([]);
    const [marketOverview, setMarketOverview] = useState(null);
    const [availableIndices, setAvailableIndices] = useState([]);
    const [availableCompanies, setAvailableCompanies] = useState([]);
    const [selectedIndexId, setSelectedIndexId] = useState('');
    const [selectedCompanySymbol, setSelectedCompanySymbol] = useState('');
    const [indexChartData, setIndexChartData] = useState([]);
    const [companyChartData, setCompanyChartData] = useState([]);
    const [indexChartLoading, setIndexChartLoading] = useState(false);
    const [companyChartLoading, setCompanyChartLoading] = useState(false);
    // New state for Top Gainers/Losers from API
    const [topGainersLosersData, setTopGainersLosersData] = useState({
        top_gainers: [],
        top_losers: []
    });
    
    // New states for market statistics
    const [totalMarketCap, setTotalMarketCap] = useState(null);
    const [avgDailyVolume, setAvgDailyVolume] = useState(null);
    const [topVolumeData, setTopVolumeData] = useState([]);
    const [marketStatistics, setMarketStatistics] = useState(null);
    const [sectorComparison, setSectorComparison] = useState([]);
    const [sectorPerformance, setSectorPerformance] = useState([]);

    const toast = useToast();

    // DataVisualization Dark Theme - Same as Dashboard2Supreme
    const bgColor = '#0a0a0a';  // Deep black background
    const cardBg = '#1a1a1a';   // Dark card background
    const borderColor = '#333'; // Dark borders
    const textColor = '#ffffff'; // White text
    const accentColor = '#6366f1'; // Purple accent
    const successColor = '#10b981'; // Green for positive
    const errorColor = '#ef4444';   // Red for negative

    // Use Top Gainers/Losers from API instead of computed data
    const topGainers = useMemo(() => {
        return topGainersLosersData.top_gainers || [];
    }, [topGainersLosersData]);
    
    const topLosers = useMemo(() => {
        return topGainersLosersData.top_losers || [];
    }, [topGainersLosersData]);

    // API Data Loading Functions
    const loadTopGainersLosers = useCallback(async () => {
        try {
            console.log('Loading Top Gainers/Losers from API...');
            const response = await xcapitalBackendService.getTopGainersLosers();
            console.log('Top Gainers/Losers API response:', response);
            
            if (response.success) {
                setTopGainersLosersData({
                    top_gainers: response.top_gainers || [],
                    top_losers: response.top_losers || []
                });
                console.log('Top Gainers/Losers loaded successfully:', {
                    gainers: response.top_gainers?.length || 0,
                    losers: response.top_losers?.length || 0
                });
            } else {
                console.warn('Top Gainers/Losers API returned no success flag');
                setTopGainersLosersData({ top_gainers: [], top_losers: [] });
            }
        } catch (error) {
            console.error('Error loading Top Gainers/Losers:', error);
            setTopGainersLosersData({ top_gainers: [], top_losers: [] });
        }
    }, []);

    // API Data Loading Functions
    const loadMarketOverview = useCallback(async () => {
        try {
            const response = await xcapitalBackendService.getMarketOverview();
            if (response.success) {
                setMarketOverview(response.data);
            }
        } catch (error) {
            console.error('Error loading market overview:', error);
        }
    }, []);

    // Fetch market statistics data from the 6 new APIs
    const fetchMarketStatistics = async () => {
        try {
            const [
                totalCapData,
                avgVolumeData,
                topVolumeData,
                marketStatsData,
                sectorCompData,
                sectorPerfData
            ] = await Promise.all([
                xcapitalBackendService.getTotalMarketCapitalization(),
                xcapitalBackendService.getAvgDailyVolume(),
                xcapitalBackendService.getTopVolumeAnalysis(),
                xcapitalBackendService.getMarketStatistics(),
                xcapitalBackendService.getSectorComparison(),
                xcapitalBackendService.getSectorPerformance()
            ]);

            setTotalMarketCap(totalCapData);
            setAvgDailyVolume(avgVolumeData);
            setTopVolumeData(topVolumeData);
            setMarketStatistics(marketStatsData);
            setSectorComparison(sectorCompData);
            setSectorPerformance(sectorPerfData);
        } catch (error) {
            console.error('Error fetching market statistics:', error);
            toast({
                title: "Error fetching market statistics",
                description: error.message,
                status: "error",
                duration: 3000,
                isClosable: true,
            });
        }
    };

    const loadAvailableIndices = useCallback(async () => {
        try {
            console.log('Loading available indices...');
            const indices = await xcapitalBackendService.getAvailableIndices();
            console.log('Available indices response:', indices);
            
            if (indices && indices.indices && Array.isArray(indices.indices)) {
                console.log('Setting available indices:', indices.indices);
                setAvailableIndices(indices.indices);
                // Only set default index if none is selected
                if (!selectedIndexId && indices.indices.length > 0) {
                    const firstIndex = indices.indices[0];
                    const indexId = firstIndex.index_id || firstIndex.id || firstIndex.symbol;
                    console.log('Setting default index ID:', indexId);
                    setSelectedIndexId(indexId);
                }
            } else if (indices && Array.isArray(indices)) {
                console.log('Setting available indices (direct array):', indices);
                setAvailableIndices(indices);
                // Only set default index if none is selected
                if (!selectedIndexId && indices.length > 0) {
                    const firstIndex = indices[0];
                    const indexId = firstIndex.index_id || firstIndex.id || firstIndex.symbol;
                    console.log('Setting default index ID:', indexId);
                    setSelectedIndexId(indexId);
                }
            } else {
                console.warn('Unexpected indices response format:', indices);
            }
        } catch (error) {
            console.error('Error loading available indices:', error);
        }
    }, [selectedIndexId]); // Add selectedIndexId to prevent unnecessary updates

    const loadIndicesData = useCallback(async () => {
        try {
            // Use the same method that works in Dashboard2Supreme
            const indices = await xcapitalBackendService.getAvailableIndices();
            if (indices && indices.indices && Array.isArray(indices.indices)) {
                setIndicesData(indices.indices);
            }
        } catch (error) {
            console.error('Error loading indices data:', error);
        }
    }, []);

    const loadAvailableCompanies = useCallback(async () => {
        try {
            console.log('Loading available companies...');
            const companies = await xcapitalBackendService.getAvailableCompanies();
            console.log('Available companies response:', companies);
            
            if (companies && companies.companies && Array.isArray(companies.companies)) {
                console.log('Setting available companies (nested):', companies.companies);
                setAvailableCompanies(companies.companies);
                // Only set default company if none is selected
                if (!selectedCompanySymbol && companies.companies.length > 0) {
                    const firstCompany = companies.companies[0];
                    console.log('Setting default company symbol:', firstCompany.symbol);
                    setSelectedCompanySymbol(firstCompany.symbol);
                }
            } else if (companies && Array.isArray(companies)) {
                console.log('Setting available companies:', companies);
                setAvailableCompanies(companies);
                // Only set default company if none is selected
                if (!selectedCompanySymbol && companies.length > 0) {
                    const firstCompany = companies[0];
                    console.log('Setting default company symbol:', firstCompany.symbol);
                    setSelectedCompanySymbol(firstCompany.symbol);
                }
            } else {
                console.warn('Unexpected companies response format:', companies);
            }
        } catch (error) {
            console.error('Error loading available companies:', error);
        }
    }, [selectedCompanySymbol]); // Add selectedCompanySymbol to prevent unnecessary updates

    const loadCompaniesData = useCallback(async () => {
        try {
            console.log('Loading companies data...');
            const companies = await xcapitalBackendService.getAvailableCompanies();
            console.log('Companies data response:', companies);
            
            if (companies && companies.companies && Array.isArray(companies.companies)) {
                console.log('Setting companies data (nested):', companies.companies);
                setCompaniesData(companies.companies);
            } else if (companies && Array.isArray(companies)) {
                console.log('Setting companies data:', companies);
                setCompaniesData(companies);
            } else {
                console.warn('Unexpected companies data format:', companies);
            }
        } catch (error) {
            console.error('Error loading companies data:', error);
        }
    }, []);

    const loadIndexChart = useCallback(async (indexId) => {
        if (!indexId) return;
        
        setIndexChartLoading(true);
        try {
            console.log('Loading index chart for:', indexId);
            const response = await xcapitalBackendService.getIndexChartData(indexId);
            console.log('Index chart response:', response);
            
            // Handle the actual API response format (no success field, direct data array)
            if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
                const chartData = response.data.map(item => ({
                    date: new Date(item.date).toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric' 
                    }),
                    value: parseFloat(item.value),
                    volume: parseFloat(item.volume || 0),
                    originalValue: item.value  // Keep original for debugging
                }));
                console.log('Processed index chart data:', chartData);
                console.log('Value range:', {
                    min: Math.min(...chartData.map(d => d.value)),
                    max: Math.max(...chartData.map(d => d.value)),
                    count: chartData.length
                });
                setIndexChartData(chartData);
            } else if (response && Array.isArray(response)) {
                // Handle direct array response
                const chartData = response.map(item => ({
                    date: new Date(item.date).toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric' 
                    }),
                    value: parseFloat(item.value),
                    volume: parseFloat(item.volume || 0)
                }));
                console.log('Processed index chart data (direct array):', chartData);
                setIndexChartData(chartData);
            } else {
                console.warn('No valid chart data available for index:', indexId, 'Response:', response);
                setIndexChartData([]);
            }
        } catch (error) {
            console.error('Error loading index chart:', error);
            setIndexChartData([]);
        } finally {
            setIndexChartLoading(false);
        }
    }, []);

    const loadCompanyChart = useCallback(async (symbol) => {
        if (!symbol) return;
        
        setCompanyChartLoading(true);
        try {
            console.log('Loading company chart for:', symbol);
            const response = await xcapitalBackendService.getCompanyChartData(symbol);
            console.log('Company chart response:', response);
            
            // Handle the actual API response format (no success field, direct data array)
            if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
                const chartData = response.data.map(item => ({
                    date: new Date(item.date).toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric' 
                    }),
                    price: parseFloat(item.price || item.close || item.value),
                    volume: parseFloat(item.volume || 0)
                }));
                console.log('Processed company chart data:', chartData);
                setCompanyChartData(chartData);
            } else if (response && Array.isArray(response)) {
                // Handle direct array response
                const chartData = response.map(item => ({
                    date: new Date(item.date).toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric' 
                    }),
                    price: parseFloat(item.price || item.close || item.value),
                    volume: parseFloat(item.volume || 0)
                }));
                console.log('Processed company chart data (direct array):', chartData);
                setCompanyChartData(chartData);
            } else {
                console.warn('No valid chart data available for company:', symbol, 'Response:', response);
                setCompanyChartData([]);
            }
        } catch (error) {
            console.error('Error loading company chart:', error);
            setCompanyChartData([]);
        } finally {
            setCompanyChartLoading(false);
        }
    }, []);

    // Load all data
    // Refresh function
    const handleRefresh = useCallback(async () => {
        setRefreshing(true);
        try {
            await Promise.all([
                loadMarketOverview(),
                loadAvailableIndices(),
                loadIndicesData(),
                loadAvailableCompanies(),
                loadCompaniesData(),
                loadTopGainersLosers(),
                fetchMarketStatistics()
            ]);
            
            if (selectedIndexId) {
                await loadIndexChart(selectedIndexId);
            }
            
            if (selectedCompanySymbol) {
                await loadCompanyChart(selectedCompanySymbol);
            }
            
            toast({
                title: 'Data refreshed',
                description: 'Market data has been updated',
                status: 'success',
                duration: 2000,
            });
        } catch (error) {
            console.error('Error refreshing data:', error);
            toast({
                title: 'Refresh failed',
                description: 'Failed to refresh market data',
                status: 'error',
                duration: 3000,
            });
        }
        setRefreshing(false);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []); // Remove dependencies to prevent infinite loops

    // Effects - Load data once on mount
    useEffect(() => {
        const loadInitialData = async () => {
            setIsLoading(true);
            try {
                await Promise.all([
                    loadMarketOverview(),
                    loadAvailableIndices(),
                    loadIndicesData(),
                    loadAvailableCompanies(),
                    loadCompaniesData(),
                    loadTopGainersLosers(),
                    fetchMarketStatistics()
                ]);
            } catch (error) {
                console.error('Error loading data:', error);
                toast({
                    title: 'Error loading data',
                    description: 'Failed to load some market data',
                    status: 'error',
                    duration: 3000,
                });
            } finally {
                setIsLoading(false);
            }
        };
        
        loadInitialData();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []); // Only run on mount

    useEffect(() => {
        // Only run chart loading if the ID actually changed and exists
        if (selectedIndexId && typeof selectedIndexId === 'string' && selectedIndexId.trim() !== '') {
            const timeoutId = setTimeout(() => {
                loadIndexChart(selectedIndexId);
            }, 100);
            return () => clearTimeout(timeoutId);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedIndexId]); // Remove loadIndexChart from dependencies

    useEffect(() => {
        // Only run chart loading if the symbol actually changed and exists
        if (selectedCompanySymbol && typeof selectedCompanySymbol === 'string' && selectedCompanySymbol.trim() !== '') {
            const timeoutId = setTimeout(() => {
                loadCompanyChart(selectedCompanySymbol);
            }, 100);
            return () => clearTimeout(timeoutId);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedCompanySymbol]); // Remove loadCompanyChart from dependencies

    // Utility functions
    const formatNumber = (num, decimals = 2) => {
        if (num === null || num === undefined || isNaN(num)) return 'N/A';
        return parseFloat(num).toFixed(decimals);
    };

    const formatCurrency = (num) => {
        if (num === null || num === undefined || isNaN(num)) return 'N/A';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'MAD',
            minimumFractionDigits: 2
        }).format(num);
    };

    // Market Stats Cards
    const MarketStatsCards = () => {
        if (!indicesData.length && !companiesData.length) {
            return (
                <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} w="full">
                    {[1, 2, 3, 4].map(i => (
                        <Skeleton key={i} height="120px" borderRadius="xl" />
                    ))}
                </SimpleGrid>
            );
        }

        const totalCompanies = companiesData.length;
        const gainers = companiesData.filter(c => (c.daily_change_pct || 0) > 0).length;
        const losers = companiesData.filter(c => (c.daily_change_pct || 0) < 0).length;
        const totalIndices = indicesData.length;

        const stats = [
            {
                label: 'Total Companies',
                value: totalCompanies,
                icon: FiBarChart2,
                color: accentColor,
                change: null
            },
            {
                label: 'Gainers',
                value: gainers,
                icon: FiTrendingUp,
                color: successColor,
                change: gainers > 0 ? `+${((gainers / totalCompanies) * 100).toFixed(1)}%` : null
            },
            {
                label: 'Losers',
                value: losers,
                icon: FiTrendingDown,
                color: errorColor,
                change: losers > 0 ? `-${((losers / totalCompanies) * 100).toFixed(1)}%` : null
            },
            {
                label: 'Total Indices',
                value: totalIndices,
                icon: FiPieChart,
                color: '#8b5cf6',
                change: null
            }
        ];

        return (
            <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} w="full">
                {stats.map((stat, index) => (
                    <MotionCard
                        key={stat.label}
                        bg={cardBg}
                        border="1px"
                        borderColor={borderColor}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                    >
                        <CardBody>
                            <Stat>
                                <Flex align="center" justify="space-between">
                                    <Box>
                                        <StatLabel color="gray.400" fontSize="sm">
                                            {stat.label}
                                        </StatLabel>
                                        <StatNumber fontSize="2xl" fontWeight="bold" color={stat.color}>
                                            {stat.value}
                                        </StatNumber>
                                        {stat.change && (
                                            <StatHelpText mb={0}>
                                                <StatArrow type={stat.change.startsWith('+') ? 'increase' : 'decrease'} />
                                                {stat.change}
                                            </StatHelpText>
                                        )}
                                    </Box>
                                    <Box p={3} bg={`${stat.color}20`} borderRadius="xl">
                                        <stat.icon size={24} color={stat.color} />
                                    </Box>
                                </Flex>
                            </Stat>
                        </CardBody>
                    </MotionCard>
                ))}
            </SimpleGrid>
        );
    };

    // Indices Chart Component
    const IndicesChart = () => (
        <MotionCard
            bg={cardBg}
            border="1px"
            borderColor={borderColor}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
        >
            <CardBody>
                <VStack align="stretch" spacing={4}>
                    <Flex justify="space-between" align="center">
                        <Heading size="md" color={textColor}>Indices Performance</Heading>
                        <Select
                            size="sm"
                            maxW="200px"
                            value={selectedIndexId}
                            onChange={(e) => {
                                const value = e.target.value;
                                if (value !== selectedIndexId) {
                                    setSelectedIndexId(value);
                                    // Load specific index chart data
                                    loadIndexChart(value);
                                }
                            }}
                            bg={cardBg}
                            color={textColor}
                            borderColor={borderColor}
                            placeholder="Select an index..."
                            _placeholder={{ color: 'gray.400' }}
                            _hover={{ borderColor: accentColor }}
                            _focus={{ 
                                borderColor: accentColor, 
                                boxShadow: `0 0 0 1px ${accentColor}` 
                            }}
                            sx={{
                                '& option': {
                                    backgroundColor: cardBg,
                                    color: textColor,
                                },
                                '& option:hover': {
                                    backgroundColor: `${accentColor}20`,
                                }
                            }}
                        >
                            {availableIndices.map(index => (
                                <option key={index.id || index.symbol || index.index_id} value={index.id || index.symbol || index.index_id}>
                                    {index.name || index.symbol || index.index_name}
                                </option>
                            ))}
                        </Select>
                    </Flex>
                    
                    <Box h="300px">
                        {indexChartLoading ? (
                            <Flex align="center" justify="center" h="100%">
                                <VStack>
                                    <Spinner size="lg" color={accentColor} />
                                    <Text color="gray.400">Loading chart data...</Text>
                                </VStack>
                            </Flex>
                        ) : indexChartData.length > 0 ? (
                            <ResponsiveContainer width="100%" height="100%">
                                <AreaChart data={indexChartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                                    <defs>
                                        <linearGradient id="indexGradient" x1="0" y1="0" x2="0" y2="1">
                                            <stop offset="5%" stopColor={accentColor} stopOpacity={0.3}/>
                                            <stop offset="95%" stopColor={accentColor} stopOpacity={0.1}/>
                                        </linearGradient>
                                    </defs>
                                    <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                                    <XAxis 
                                        dataKey="date" 
                                        tick={{ fontSize: 12, fill: textColor }}
                                        tickLine={false}
                                        axisLine={{ stroke: borderColor, strokeWidth: 1 }}
                                    />
                                    <YAxis 
                                        tick={{ fontSize: 12, fill: textColor }}
                                        tickLine={false}
                                        axisLine={false}
                                        domain={['dataMin - 100', 'dataMax + 100']}
                                        tickFormatter={(value) => value.toLocaleString()}
                                    />
                                    <Tooltip 
                                        contentStyle={{ 
                                            backgroundColor: cardBg, 
                                            border: `1px solid ${borderColor}`,
                                            borderRadius: '8px',
                                            color: textColor
                                        }}
                                        formatter={(value, name) => [
                                            value.toLocaleString('en-US', { minimumFractionDigits: 2 }), 
                                            'Index Value'
                                        ]}
                                        labelFormatter={(label) => `Date: ${label}`}
                                    />
                                    <Area
                                        type="monotone"
                                        dataKey="value"
                                        stroke={accentColor}
                                        fill="url(#indexGradient)"
                                        strokeWidth={3}
                                        dot={{ fill: accentColor, strokeWidth: 2, r: 4 }}
                                        activeDot={{ r: 6, stroke: accentColor, strokeWidth: 2 }}
                                        connectNulls={true}
                                    />
                                </AreaChart>
                            </ResponsiveContainer>
                        ) : (
                            <Flex align="center" justify="center" h="100%">
                                <VStack>
                                    <Icon as={FiBarChart2} boxSize={8} color="gray.400" />
                                    <Text color="gray.400">
                                        {selectedIndexId ? 'No chart data available for this index' : 'Select an index to view chart'}
                                    </Text>
                                </VStack>
                            </Flex>
                        )}
                    </Box>
                </VStack>
            </CardBody>
        </MotionCard>
    );

    // Companies Chart Component
    const CompaniesChart = () => (
        <MotionCard
            bg={cardBg}
            border="1px"
            borderColor={borderColor}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
        >
            <CardBody>
                <VStack align="stretch" spacing={4}>
                    <Flex justify="space-between" align="center">
                        <Heading size="md" color={textColor}>Company Performance</Heading>
                        <Select
                            size="sm"
                            maxW="200px"
                            value={selectedCompanySymbol}
                            onChange={(e) => {
                                const value = e.target.value;
                                if (value !== selectedCompanySymbol) {
                                    setSelectedCompanySymbol(value);
                                    // Load specific company chart data
                                    loadCompanyChart(value);
                                }
                            }}
                            bg={cardBg}
                            color={textColor}
                            borderColor={borderColor}
                            placeholder="Select a company..."
                            _placeholder={{ color: 'gray.400' }}
                            _hover={{ borderColor: accentColor }}
                            _focus={{ 
                                borderColor: accentColor, 
                                boxShadow: `0 0 0 1px ${accentColor}` 
                            }}
                            sx={{
                                '& option': {
                                    backgroundColor: cardBg,
                                    color: textColor,
                                },
                                '& option:hover': {
                                    backgroundColor: `${accentColor}20`,
                                }
                            }}
                        >
                            {availableCompanies.map(company => (
                                <option key={company.symbol} value={company.symbol}>
                                    {company.nom_francais || company.name || company.symbol}
                                </option>
                            ))}
                        </Select>
                    </Flex>
                    
                    <Box h="300px">
                        {companyChartLoading ? (
                            <Flex align="center" justify="center" h="100%">
                                <VStack>
                                    <Spinner size="lg" color={successColor} />
                                    <Text color="gray.400">Loading chart data...</Text>
                                </VStack>
                            </Flex>
                        ) : companyChartData.length > 0 ? (
                            <ResponsiveContainer width="100%" height="100%">
                                <LineChart data={companyChartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                                    <defs>
                                        <linearGradient id="companyGradient" x1="0" y1="0" x2="0" y2="1">
                                            <stop offset="5%" stopColor={successColor} stopOpacity={0.3}/>
                                            <stop offset="95%" stopColor={successColor} stopOpacity={0.1}/>
                                        </linearGradient>
                                    </defs>
                                    <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                                    <XAxis 
                                        dataKey="date" 
                                        tick={{ fontSize: 12, fill: textColor }}
                                        tickLine={false}
                                        axisLine={{ stroke: borderColor, strokeWidth: 1 }}
                                    />
                                    <YAxis 
                                        tick={{ fontSize: 12, fill: textColor }}
                                        tickLine={false}
                                        axisLine={false}
                                        domain={['dataMin - 10', 'dataMax + 10']}
                                        tickFormatter={(value) => value.toLocaleString()}
                                    />
                                    <Tooltip 
                                        contentStyle={{ 
                                            backgroundColor: cardBg, 
                                            border: `1px solid ${borderColor}`,
                                            borderRadius: '8px',
                                            color: textColor
                                        }}
                                        formatter={(value, name) => [
                                            value.toLocaleString('en-US', { minimumFractionDigits: 2 }), 
                                            'Stock Price'
                                        ]}
                                        labelFormatter={(label) => `Date: ${label}`}
                                    />
                                    <Line
                                        type="monotone"
                                        dataKey="price"
                                        stroke={successColor}
                                        strokeWidth={3}
                                        dot={{ fill: successColor, strokeWidth: 2, r: 4 }}
                                        activeDot={{ r: 6, stroke: successColor, strokeWidth: 2 }}
                                        connectNulls={true}
                                    />
                                </LineChart>
                            </ResponsiveContainer>
                        ) : (
                            <Flex align="center" justify="center" h="100%">
                                <VStack>
                                    <Icon as={FiTrendingUp} boxSize={8} color="gray.400" />
                                    <Text color="gray.400">
                                        {selectedCompanySymbol ? 'No chart data available for this company' : 'Select a company to view chart'}
                                    </Text>
                                </VStack>
                            </Flex>
                        )}
                    </Box>
                </VStack>
            </CardBody>
        </MotionCard>
    );

    // Top Movers Table
    const TopMoversTable = ({ title, data, isGainers = true }) => (
        <MotionCard
            bg={cardBg}
            border="1px"
            borderColor={borderColor}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
        >
            <CardBody>
                <VStack align="stretch" spacing={4}>
                    <Heading size="md" color={textColor}>{title}</Heading>
                    <TableContainer>
                        <Table size="xs" variant="simple">
                            <Thead>
                                <Tr>
                                    <Th fontSize="xs" fontWeight="600" color="gray.300" borderColor="#333">Symbol</Th>
                                    <Th fontSize="xs" fontWeight="600" color="gray.300" borderColor="#333">Name</Th>
                                    <Th fontSize="xs" fontWeight="600" color="gray.300" borderColor="#333" isNumeric>Price</Th>
                                    <Th fontSize="xs" fontWeight="600" color="gray.300" borderColor="#333" isNumeric>Change %</Th>
                                </Tr>
                            </Thead>
                            <Tbody>
                                {data.length > 0 ? data.map((company, index) => (
                                    <Tr key={`${company.symbol}-${index}`} _hover={{ bg: '#222' }}>
                                        <Td 
                                            fontWeight="600" 
                                            fontSize="xs" 
                                            color={textColor} 
                                            borderColor="#333"
                                            py={2}
                                        >
                                            {company.symbol}
                                        </Td>
                                        <Td 
                                            fontSize="xs" 
                                            color="gray.400" 
                                            maxW="120px" 
                                            isTruncated
                                            borderColor="#333"
                                            py={2}
                                        >
                                            {company.company_name || company.name || company.symbol}
                                        </Td>
                                        <Td 
                                            isNumeric 
                                            fontWeight="500" 
                                            fontSize="xs" 
                                            color={textColor}
                                            borderColor="#333"
                                            py={2}
                                        >
                                            {formatCurrency(company.current_price || company.price)}
                                        </Td>
                                        <Td isNumeric borderColor="#333" py={2}>
                                            <Badge
                                                colorScheme={isGainers ? 'green' : 'red'}
                                                variant="solid"
                                                fontSize="10px"
                                                px={2}
                                                py={1}
                                                borderRadius="md"
                                            >
                                                {isGainers ? '+' : ''}{formatNumber(company.diff_percentage || company.daily_change_pct || 0, 2)}%
                                            </Badge>
                                        </Td>
                                    </Tr>
                                )) : (
                                    <Tr>
                                        <Td colSpan={4} textAlign="center" color="gray.400" fontSize="xs" py={4} borderColor="#333">
                                            No data available
                                        </Td>
                                    </Tr>
                                )}
                            </Tbody>
                        </Table>
                    </TableContainer>
                </VStack>
            </CardBody>
        </MotionCard>
    );

    if (isLoading) {
        return (
            <Box minH="100vh" bg={bgColor} p={6}>
                <Container maxW="full">
                    <VStack spacing={6}>
                        <Skeleton height="80px" borderRadius="xl" />
                        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} w="full">
                            {[1, 2, 3, 4].map(i => (
                                <Skeleton key={i} height="150px" borderRadius="xl" />
                            ))}
                        </SimpleGrid>
                        <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6} w="full">
                            <Skeleton height="450px" borderRadius="xl" />
                            <Skeleton height="450px" borderRadius="xl" />
                        </SimpleGrid>
                    </VStack>
                </Container>
            </Box>
        );
    }

    return (
        <Box minH="100vh" bg={bgColor} p={6}>
            <Container maxW="full">
                <VStack spacing={6} align="stretch">
                    {/* Header */}
                    <MotionBox
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                    >
                        <Card bg={cardBg} border="1px" borderColor={borderColor}>
                            <CardBody>
                                <Flex justify="space-between" align="center">
                                    <VStack align="start" spacing={1}>
                                        <Heading size="lg" color={textColor}>
                                            Professional Market Overview
                                        </Heading>
                                        <Text color="gray.400" fontSize="sm">
                                            Real-time market data and analytics
                                        </Text>
                                    </VStack>
                                    <ButtonGroup>
                                        <IconButton
                                            icon={<FiRefreshCw />}
                                            aria-label="Refresh data"
                                            isLoading={refreshing}
                                            isDisabled={refreshing}
                                            onClick={handleRefresh}
                                            colorScheme="purple"
                                            variant="outline"
                                            _hover={{ 
                                                bg: `${accentColor}20`,
                                                borderColor: accentColor 
                                            }}
                                        />
                                    </ButtonGroup>
                                </Flex>
                            </CardBody>
                        </Card>
                    </MotionBox>

                    {/* Market Stats */}
                    <MarketStatsCards />

                    {/* New Comprehensive Market Statistics */}
                    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
                        {/* Total Market Capitalization */}
                        {totalMarketCap && (
                            <MotionCard
                                bg={cardBg}
                                border="1px"
                                borderColor={borderColor}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.2 }}
                                _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg' }}
                            >
                                <CardBody>
                                    <VStack align="stretch" spacing={4}>
                                        <Flex align="center" gap={3}>
                                            <Box
                                                p={2}
                                                borderRadius="lg"
                                                bg={`${accentColor}20`}
                                            >
                                                <FiDollarSign color={accentColor} size={20} />
                                            </Box>
                                            <VStack align="start" spacing={0}>
                                                <Text fontSize="sm" color="gray.400">
                                                    Total Market Cap
                                                </Text>
                                                <Text fontSize="2xl" fontWeight="bold" color={textColor}>
                                                    {formatNumber(totalMarketCap.total_market_cap, 0)} MAD
                                                </Text>
                                            </VStack>
                                        </Flex>
                                        <Divider borderColor={borderColor} />
                                        <SimpleGrid columns={2} spacing={4}>
                                            <Box>
                                                <Text fontSize="xs" color="gray.400">Companies</Text>
                                                <Text fontSize="lg" fontWeight="semibold" color={textColor}>
                                                    {totalMarketCap.total_companies}
                                                </Text>
                                            </Box>
                                            <Box>
                                                <Text fontSize="xs" color="gray.400">Last Updated</Text>
                                                <Text fontSize="xs" color="gray.300">
                                                    {totalMarketCap.last_updated}
                                                </Text>
                                            </Box>
                                        </SimpleGrid>
                                    </VStack>
                                </CardBody>
                            </MotionCard>
                        )}

                        {/* Average Daily Volume */}
                        {avgDailyVolume && (
                            <MotionCard
                                bg={cardBg}
                                border="1px"
                                borderColor={borderColor}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.3 }}
                                _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg' }}
                            >
                                <CardBody>
                                    <VStack align="stretch" spacing={4}>
                                        <Flex align="center" gap={3}>
                                            <Box
                                                p={2}
                                                borderRadius="lg"
                                                bg={`${accentColor}20`}
                                            >
                                                <FiActivity color={accentColor} size={20} />
                                            </Box>
                                            <VStack align="start" spacing={0}>
                                                <Text fontSize="sm" color="gray.400">
                                                    Avg Daily Volume
                                                </Text>
                                                <Text fontSize="2xl" fontWeight="bold" color={textColor}>
                                                    {formatNumber(avgDailyVolume.avg_daily_volume, 0)}
                                                </Text>
                                            </VStack>
                                        </Flex>
                                        <Divider borderColor={borderColor} />
                                        <SimpleGrid columns={2} spacing={4}>
                                            <Box>
                                                <Text fontSize="xs" color="gray.400">Companies</Text>
                                                <Text fontSize="lg" fontWeight="semibold" color={textColor}>
                                                    {avgDailyVolume.total_companies}
                                                </Text>
                                            </Box>
                                            <Box>
                                                <Text fontSize="xs" color="gray.400">Last Updated</Text>
                                                <Text fontSize="xs" color="gray.300">
                                                    {avgDailyVolume.last_updated}
                                                </Text>
                                            </Box>
                                        </SimpleGrid>
                                    </VStack>
                                </CardBody>
                            </MotionCard>
                        )}

                        {/* Market Statistics Summary */}
                        {marketStatistics && (
                            <MotionCard
                                bg={cardBg}
                                border="1px"
                                borderColor={borderColor}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.4 }}
                                _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg' }}
                            >
                                <CardBody>
                                    <VStack align="stretch" spacing={4}>
                                        <Flex align="center" gap={3}>
                                            <Box
                                                p={2}
                                                borderRadius="lg"
                                                bg={`${accentColor}20`}
                                            >
                                                <FiTrendingUp color={accentColor} size={20} />
                                            </Box>
                                            <VStack align="start" spacing={0}>
                                                <Text fontSize="sm" color="gray.400">
                                                    Market Sentiment
                                                </Text>
                                                <Text fontSize="xl" fontWeight="bold" color={getMarketSentimentColor(marketStatistics.market_sentiment)}>
                                                    {getMarketSentimentDisplay(marketStatistics.market_sentiment)}
                                                </Text>
                                            </VStack>
                                        </Flex>
                                        <Divider borderColor={borderColor} />
                                        <SimpleGrid columns={2} spacing={4}>
                                            <Box>
                                                <Text fontSize="xs" color="gray.400">Total Volume</Text>
                                                <Text fontSize="sm" fontWeight="semibold" color={textColor}>
                                                    {formatNumber(marketStatistics.total_volume, 0)}
                                                </Text>
                                            </Box>
                                            <Box>
                                                <Text fontSize="xs" color="gray.400">Avg Price</Text>
                                                <Text fontSize="sm" fontWeight="semibold" color={textColor}>
                                                    {formatNumber(marketStatistics.avg_price, 2)} MAD
                                                </Text>
                                            </Box>
                                        </SimpleGrid>
                                    </VStack>
                                </CardBody>
                            </MotionCard>
                        )}
                    </SimpleGrid>

                    {/* Top Volume Analysis */}
                    {topVolumeData && topVolumeData.length > 0 && (
                        <MotionCard
                            bg={cardBg}
                            border="1px"
                            borderColor={borderColor}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.5 }}
                        >
                            <CardBody>
                                <VStack align="stretch" spacing={6}>
                                    <Flex align="center" gap={3}>
                                        <Box
                                            p={2}
                                            borderRadius="lg"
                                            bg={`${accentColor}20`}
                                        >
                                            <FiBarChart color={accentColor} size={20} />
                                        </Box>
                                        <Heading size="md" color={textColor}>Top 5 Volume Analysis</Heading>
                                    </Flex>
                                    <TableContainer>
                                        <Table variant="simple" size="sm">
                                            <Thead>
                                                <Tr>
                                                    <Th color="gray.400" fontSize="xs" borderColor="#333">Rank</Th>
                                                    <Th color="gray.400" fontSize="xs" borderColor="#333">Company</Th>
                                                    <Th color="gray.400" fontSize="xs" borderColor="#333">Daily Volume</Th>
                                                    <Th color="gray.400" fontSize="xs" borderColor="#333">Price</Th>
                                                    <Th color="gray.400" fontSize="xs" borderColor="#333">Market Cap</Th>
                                                </Tr>
                                            </Thead>
                                            <Tbody>
                                                {topVolumeData.map((company, index) => (
                                                    <Tr key={company.symbol || index}>
                                                        <Td color={textColor} fontSize="xs" borderColor="#333">
                                                            <Badge
                                                                colorScheme={index < 3 ? 'purple' : 'gray'}
                                                                variant="subtle"
                                                                fontSize="xs"
                                                            >
                                                                #{index + 1}
                                                            </Badge>
                                                        </Td>
                                                        <Td color={textColor} fontSize="xs" borderColor="#333">
                                                            <VStack align="start" spacing={0}>
                                                                <Text fontWeight="semibold">{company.company_name}</Text>
                                                                <Text color="gray.400" fontSize="10px">{company.symbol}</Text>
                                                            </VStack>
                                                        </Td>
                                                        <Td color={textColor} fontSize="xs" borderColor="#333">
                                                            <Text fontWeight="bold" color={accentColor}>
                                                                {formatNumber(company.daily_volume, 0)}
                                                            </Text>
                                                        </Td>
                                                        <Td color={textColor} fontSize="xs" borderColor="#333">
                                                            {formatNumber(company.current_price, 2)} MAD
                                                        </Td>
                                                        <Td color={textColor} fontSize="xs" borderColor="#333">
                                                            {formatNumber(company.market_cap, 0)} MAD
                                                        </Td>
                                                    </Tr>
                                                ))}
                                            </Tbody>
                                        </Table>
                                    </TableContainer>
                                </VStack>
                            </CardBody>
                        </MotionCard>
                    )}

                    {/* Sector Performance and Comparison */}
                    <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                        {/* Sector Comparison */}
                        {sectorComparison && sectorComparison.length > 0 && (
                            <MotionCard
                                bg={cardBg}
                                border="1px"
                                borderColor={borderColor}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.6 }}
                            >
                                <CardBody>
                                    <VStack align="stretch" spacing={6}>
                                        <Flex align="center" gap={3}>
                                            <Box
                                                p={2}
                                                borderRadius="lg"
                                                bg={`${accentColor}20`}
                                            >
                                                <FiPieChart color={accentColor} size={20} />
                                            </Box>
                                            <Heading size="md" color={textColor}>Sector Comparison</Heading>
                                        </Flex>
                                        <VStack spacing={4}>
                                            {sectorComparison.map((sector, index) => (
                                                <Box
                                                    key={sector.index_name || index}
                                                    p={4}
                                                    bg={`${accentColor}05`}
                                                    borderRadius="lg"
                                                    border="1px"
                                                    borderColor={`${accentColor}20`}
                                                    w="full"
                                                    _hover={{ bg: `${accentColor}10` }}
                                                >
                                                    <Flex justify="space-between" align="center">
                                                        <VStack align="start" spacing={0}>
                                                            <Text fontWeight="semibold" color={textColor}>
                                                                {sector.index_name}
                                                            </Text>
                                                            <Text fontSize="sm" color="gray.400">
                                                                Value: {formatNumber(sector.latest_value, 2)}
                                                            </Text>
                                                        </VStack>
                                                        <Badge
                                                            colorScheme={sector.daily_change_pct >= 0 ? 'green' : 'red'}
                                                            variant="subtle"
                                                            px={3}
                                                            py={1}
                                                            borderRadius="md"
                                                        >
                                                            {sector.daily_change_pct >= 0 ? '+' : ''}
                                                            {formatNumber(sector.daily_change_pct, 2)}%
                                                        </Badge>
                                                    </Flex>
                                                </Box>
                                            ))}
                                        </VStack>
                                    </VStack>
                                </CardBody>
                            </MotionCard>
                        )}

                        {/* Sector Performance Bar Chart */}
                        {sectorPerformance && sectorPerformance.length > 0 && (
                            <MotionCard
                                bg={cardBg}
                                border="1px"
                                borderColor={borderColor}
                                initial={{ opacity: 0, x: 20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.7 }}
                            >
                                <CardBody>
                                    <VStack align="stretch" spacing={6}>
                                        <Flex align="center" gap={3}>
                                            <Box
                                                p={2}
                                                borderRadius="lg"
                                                bg={`${accentColor}20`}
                                            >
                                                <FiBarChart color={accentColor} size={20} />
                                            </Box>
                                            <Heading size="md" color={textColor}>Sector Performance</Heading>
                                        </Flex>
                                        <VStack spacing={3}>
                                            {sectorPerformance.map((sector, index) => {
                                                const performance = sector.daily_change_pct || 0;
                                                const isPositive = performance >= 0;
                                                const barWidth = Math.abs(performance) * 10; // Scale for visual
                                                
                                                return (
                                                    <Box key={sector.index_name || index} w="full">
                                                        <Flex justify="space-between" align="center" mb={2}>
                                                            <Text fontSize="sm" color={textColor} fontWeight="medium">
                                                                {sector.index_name}
                                                            </Text>
                                                            <Text
                                                                fontSize="sm"
                                                                color={isPositive ? 'green.400' : 'red.400'}
                                                                fontWeight="bold"
                                                            >
                                                                {isPositive ? '+' : ''}{formatNumber(performance, 2)}%
                                                            </Text>
                                                        </Flex>
                                                        <Box
                                                            w="full"
                                                            h="6px"
                                                            bg="gray.700"
                                                            borderRadius="full"
                                                            overflow="hidden"
                                                        >
                                                            <Box
                                                                h="full"
                                                                w={`${Math.min(Math.abs(performance) * 20, 100)}%`}
                                                                bg={isPositive ? 'green.400' : 'red.400'}
                                                                borderRadius="full"
                                                                transition="width 0.3s ease"
                                                            />
                                                        </Box>
                                                    </Box>
                                                );
                                            })}
                                        </VStack>
                                    </VStack>
                                </CardBody>
                            </MotionCard>
                        )}
                    </SimpleGrid>

                    {/* Charts */}
                    <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                        <IndicesChart />
                        <CompaniesChart />
                    </SimpleGrid>

                    {/* Top Movers */}
                    <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                        <TopMoversTable 
                            title="Top Gainers" 
                            data={topGainers} 
                            isGainers={true} 
                        />
                        <TopMoversTable 
                            title="Top Losers" 
                            data={topLosers} 
                            isGainers={false} 
                        />
                    </SimpleGrid>

                    {/* Market Overview Data */}
                    {marketOverview && (
                        <MotionCard
                            bg={cardBg}
                            border="1px"
                            borderColor={borderColor}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.6 }}
                        >
                            <CardBody>
                                <VStack align="stretch" spacing={4}>
                                    <Heading size="md" color={textColor}>Market Overview</Heading>
                                    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
                                        {Object.entries(marketOverview).map(([key, value]) => (
                                            <Box key={key} p={4} bg={`${accentColor}10`} borderRadius="lg">
                                                <Text fontSize="sm" color="gray.400" textTransform="capitalize">
                                                    {key.replace(/_/g, ' ')}
                                                </Text>
                                                <Text fontSize="lg" fontWeight="bold" color={textColor}>
                                                    {typeof value === 'number' ? formatNumber(value) : value}
                                                </Text>
                                            </Box>
                                        ))}
                                    </SimpleGrid>
                                </VStack>
                            </CardBody>
                        </MotionCard>
                    )}
                </VStack>
            </Container>
        </Box>
    );
};

export default ProfessionalMarketOverview;
