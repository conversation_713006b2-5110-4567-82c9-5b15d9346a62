import React, { useState, useEffect } from 'react';
import {
    Box,
    HStack,
    Text,
    Icon
} from '@chakra-ui/react';
import { FiTrendingUp, FiTrendingDown } from 'react-icons/fi';
import xcapitalBackendService from '../../../services/xcapitalBackendService';

const StockTicker = () => {
    const [tickerData, setTickerData] = useState([]);
    const [displayData, setDisplayData] = useState([]);
    const [hasInitialized, setHasInitialized] = useState(false);

    // DataVisualization theme colors
    const tickerBg = '#1a1a1a';
    const borderColor = '#333333';
    const textColor = '#ffffff';
    const greenColor = '#00ff88';
    const redColor = '#ff4757';

    // Mock data fallback for demonstration
    const mockTickerData = [
        { symbol: 'CIH', name: '<PERSON>rédit Immobilier et Hôtelier', price: '324.50', change: '2.34', isPositive: true },
        { symbol: 'BCP', name: 'Banque Centrale Populaire', price: '278.90', change: '1.56', isPositive: true },
        { symbol: 'BOA', name: 'Bank Of Africa', price: '432.10', change: '0.89', isPositive: false },
        { symbol: 'ATW', name: 'Attijariwafa Bank', price: '512.75', change: '3.21', isPositive: true },
        { symbol: 'LHM', name: 'LafargeHolcim Maroc', price: '1876.00', change: '2.10', isPositive: false },
        { symbol: 'CSR', name: 'Cosumar', price: '234.60', change: '1.45', isPositive: true },
        { symbol: 'MNG', name: 'Managem', price: '1245.50', change: '4.32', isPositive: true },
        { symbol: 'CDM', name: 'Ciments du Maroc', price: '1890.25', change: '1.78', isPositive: false },
        { symbol: 'SNP', name: 'Sonasid', price: '845.30', change: '0.95', isPositive: true },
        { symbol: 'TQM', name: 'Taqa Morocco', price: '923.40', change: '2.67', isPositive: false },
        { symbol: 'IAM', name: 'Itissalat Al-Maghrib', price: '156.80', change: '1.23', isPositive: true },
        { symbol: 'MSA', name: 'Marsa Maroc', price: '678.90', change: '0.87', isPositive: true },
        { symbol: 'ALM', name: 'Aluminium du Maroc', price: '1234.75', change: '3.45', isPositive: false },
        { symbol: 'ADI', name: 'Addoha', price: '89.50', change: '2.11', isPositive: true },
        { symbol: 'SNA', name: 'Société Nationale d\'Autoroutes', price: '1456.80', change: '1.89', isPositive: false }
    ];

    // Load ticker data from backend APIs with fallback to mock data
    const loadTickerData = async () => {
        try {
            console.log('🎯 Loading ticker data from backend...');

            // Try to get real companies data
            const companies = await xcapitalBackendService.getAvailableCompanies();
            console.log('📊 Available companies:', companies?.length || 0);

            let newData;
            if (companies && companies.length > 0) {
                // Create ticker data using real companies with simulated price changes
                newData = companies.slice(0, 15).map((company, index) => {
                    // Generate realistic stock price variations
                    const basePrice = 100 + (index * 50) + (Math.random() * 500);
                    const change = (Math.random() - 0.5) * 10; // -5% to +5%
                    const isPositive = change >= 0;

                    return {
                        symbol: company.symbol,
                        name: company.nom_francais || company.name || company.symbol,
                        price: basePrice.toFixed(2),
                        change: Math.abs(change).toFixed(2),
                        isPositive: isPositive
                    };
                });

                console.log('✅ Generated ticker data from real companies:', newData.length, 'stocks');
            } else {
                // Fallback to mock data
                console.log('🔄 No real companies data, using mock ticker data');
                newData = mockTickerData;
            }

            setTickerData(newData);
            
            // Only set display data on first load to avoid animation restart
            if (!hasInitialized) {
                setDisplayData(newData);
                setHasInitialized(true);
            }

        } catch (error) {
            console.error('❌ Error loading ticker data, falling back to mock data:', error);
            const fallbackData = mockTickerData;
            setTickerData(fallbackData);
            
            if (!hasInitialized) {
                setDisplayData(fallbackData);
                setHasInitialized(true);
            }
        }
    };

    useEffect(() => {
        loadTickerData();
        
        // Refresh ticker data every 30 seconds without restarting animation
        const interval = setInterval(() => {
            loadTickerData();
        }, 30000);
        
        return () => clearInterval(interval);
    }, []); // eslint-disable-line react-hooks/exhaustive-deps

    // Update display data smoothly every 3 seconds to refresh prices without restarting animation
    useEffect(() => {
        if (tickerData.length > 0 && hasInitialized) {
            const updateInterval = setInterval(() => {
                // Update prices with small variations to simulate real-time changes
                const updatedData = tickerData.map(stock => ({
                    ...stock,
                    price: (parseFloat(stock.price) * (0.98 + Math.random() * 0.04)).toFixed(2), // ±2% variation
                    change: (Math.random() * 5).toFixed(2),
                    isPositive: Math.random() > 0.5
                }));
                setDisplayData(updatedData);
            }, 3000);

            return () => clearInterval(updateInterval);
        }
    }, [tickerData, hasInitialized]);

    const formatPrice = (price) => {
        return parseFloat(price).toFixed(2);
    };

    const formatChange = (change, isPositive) => {
        const sign = isPositive ? '+' : '-';
        return `${sign}${parseFloat(change).toFixed(2)}%`;
    };

    // Only show loading on initial load, not on refresh
    if (!hasInitialized && displayData.length === 0) {
        return (
            <Box
                bg={tickerBg}
                borderBottom={`1px solid ${borderColor}`}
                p={3}
                overflow="hidden"
                position="relative"
                height="60px"
                display="flex"
                alignItems="center"
                justifyContent="center"
            >
                <Text color="gray.400" fontSize="sm">Loading market data...</Text>
            </Box>
        );
    }

    return (
        <Box
            bg={tickerBg}
            borderBottom={`1px solid ${borderColor}`}
            p={3}
            overflow="hidden"
            position="relative"
            height="60px"
        >
            <Box
                animation="scroll 25s linear infinite"
                display="flex"
                alignItems="center"
                height="100%"
                width="max-content"
                _hover={{
                    animationPlayState: 'paused'
                }}
                sx={{
                    '@keyframes scroll': {
                        '0%': {
                            transform: 'translateX(0%)'
                        },
                        '100%': {
                            transform: 'translateX(-50%)'
                        }
                    }
                }}
            >
                <HStack spacing={8} whiteSpace="nowrap">
                    {/* Create seamless loop by duplicating data exactly twice and animating by -50% */}
                    {[...displayData, ...displayData].map((stock, index) => (
                        <HStack key={`${stock.symbol}-${index}`} spacing={3} minW="200px">
                            <Text
                                fontWeight="bold"
                                color={textColor}
                                fontSize="sm"
                            >
                                {stock.symbol}
                            </Text>
                            <Text
                                color={textColor}
                                fontSize="sm"
                            >
                                ${formatPrice(stock.price)}
                            </Text>
                            <HStack spacing={1}>
                                <Icon
                                    as={stock.isPositive ? FiTrendingUp : FiTrendingDown}
                                    color={stock.isPositive ? greenColor : redColor}
                                    boxSize={3}
                                />
                                <Text
                                    color={stock.isPositive ? greenColor : redColor}
                                    fontSize="sm"
                                    fontWeight="medium"
                                >
                                    {formatChange(stock.change, stock.isPositive)}
                                </Text>
                            </HStack>
                        </HStack>
                    ))}
                </HStack>
            </Box>
        </Box>
    );
};

export default StockTicker;
