import React, { useState, useEffect, useCallback } from 'react';
import {
    Box,
    VStack,
    HStack,
    Text,
    Heading,
    Card,
    CardBody,
    CardHeader,
    Badge,
    Button,
    Tabs,
    TabList,
    TabPanels,
    Tab,
    TabPanel,
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    TableContainer,
    Select,
    useToast,
    Skeleton,
    FormControl,
    FormLabel,
    Input,
    Avatar,
    SimpleGrid,
    Spinner,
    Center,
    Icon
} from '@chakra-ui/react';
import {
    ResponsiveContainer,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ComposedChart,
    Area,
    Bar
} from 'recharts';
import {
    FiTrendingUp,
    FiBarChart2,
    FiBarChart,
    FiGlobe,
    FiActivity,
    FiPieChart,
    FiRefreshCw,
    FiCalendar,
    FiTarget,
    FiUsers
} from 'react-icons/fi';
import {
    MdAnalytics,
    MdShowChart,
    MdCorporateFare
} from 'react-icons/md';
import { motion } from 'framer-motion';
import xcapitalBackendService from '../../../services/xcapitalBackendService';
import ProfessionalMarketOverview from './ProfessionalMarketOverview';
import StockTicker from './StockTicker';

const MotionBox = motion(Box);
const MotionCard = motion(Card);

// Safe renderer for any data type to prevent React object rendering errors
const SafeDataRenderer = ({ data, fallback = 'N/A' }) => {
    if (data === null || data === undefined) return fallback;
    if (typeof data === 'object') {
        // Don't render objects directly - stringify them or return fallback for sensitive objects
        if (data.positive_companies !== undefined || data.negative_companies !== undefined) {
            console.warn('Market sentiment object detected and converted:', data);
            return `Sentiment: ${data.positive_percentage || 0}% positive`;
        }
        return JSON.stringify(data);
    }
    return String(data);
};

// Enhanced object safety filter - removes problematic nested objects
const cleanObjectForRendering = (obj) => {
    if (!obj || typeof obj !== 'object') return obj;
    
    const cleaned = { ...obj };
    
    // Remove market_sentiment objects that cause React rendering errors
    if (cleaned.market_sentiment && typeof cleaned.market_sentiment === 'object') {
        console.log('Removing market_sentiment object to prevent React error:', cleaned.market_sentiment);
        delete cleaned.market_sentiment;
    }
    
    // Clean nested objects recursively
    Object.keys(cleaned).forEach(key => {
        if (cleaned[key] && typeof cleaned[key] === 'object') {
            // Check if this object has the problematic keys
            if (cleaned[key].positive_companies !== undefined || 
                cleaned[key].negative_companies !== undefined ||
                cleaned[key].neutral_companies !== undefined) {
                console.log(`Removing problematic sentiment object from ${key}:`, cleaned[key]);
                delete cleaned[key];
            }
        }
    });
    
    return cleaned;
};

const Dashboard2Supreme = () => {
    const [isLoading, setIsLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [activeTab, setActiveTab] = useState(0);
    const [realTimeMode] = useState(false); // setRealTimeMode removed with header controls
    
    // Market Data States
    const [indicesData, setIndicesData] = useState([]);
    const [companiesData, setCompaniesData] = useState([]);
    
    // New Market Statistics States
    const [totalMarketCap, setTotalMarketCap] = useState(null);
    const [avgDailyVolume, setAvgDailyVolume] = useState(null);
    const [topVolumeAnalysis, setTopVolumeAnalysis] = useState({});
    const [marketStatistics, setMarketStatistics] = useState(null);
    const [sectorComparison, setSectorComparison] = useState([]);
    const [sectorPerformance, setSectorPerformance] = useState([]);
    
    // Indices Analysis States
    const [selectedAnalysisIndex, setSelectedAnalysisIndex] = useState('512312'); // MASI ID
    const [analysisPeriod, setAnalysisPeriod] = useState('1Y');
    const [customStartDate, setCustomStartDate] = useState('2022-08-22'); // Data start date
    const [customEndDate, setCustomEndDate] = useState(new Date().toISOString().split('T')[0]); // Today
    const [indexValuesData, setIndexValuesData] = useState([]);
    const [loadingIndexValues, setLoadingIndexValues] = useState(false);
    
    // Company Analysis States
    const [availableCompanies, setAvailableCompanies] = useState([]);
    const [selectedCompany, setSelectedCompany] = useState('');
    const [companyPeriod, setCompanyPeriod] = useState('1Y');
    const [companyCustomStartDate, setCompanyCustomStartDate] = useState('2022-08-22');
    const [companyCustomEndDate, setCompanyCustomEndDate] = useState(new Date().toISOString().split('T')[0]);
    const [companyChartData, setCompanyChartData] = useState([]);
    const [companyPricesData, setCompanyPricesData] = useState([]);
    const [loadingCompanyData, setLoadingCompanyData] = useState(false);
    const [companyDetails, setCompanyDetails] = useState(null);
    
    const toast = useToast();
    
    // DataVisualization Dark Theme - Professional color scheme
    const bgGradient = '#0a0a0a'; // Dark background like DataVisualization
    const cardBg = '#1a1a1a'; // Dark card background
    const borderColor = '#333'; // Dark border color
    const inputBg = '#1a1a1a'; // Dark input background
    const accentColor = '#ffce30'; // Keep the signature yellow
    const successColor = '#4ade80';
    const errorColor = '#f87171';

    // Data Loading Functions
    const loadMarketOverview = useCallback(async () => {
        try {
            const overview = await xcapitalBackendService.getMarketOverview();
            console.log('Market overview loaded:', overview);
            
            if (overview && overview.market_overview) {
                const { top_companies_by_volume } = overview;
                
                // Convert volume data for charts
                if (top_companies_by_volume && top_companies_by_volume.length > 0) {
                    const volumeData = top_companies_by_volume.map(company => ({
                        name: company.symbol,
                        volume: Math.round(company.recent_volume / 1000000), // Convert to millions
                        fullName: company.name,
                        rawVolume: company.recent_volume
                    }));
                    console.log('Trading volume data loaded:', volumeData.length, 'companies');
                }
            }
        } catch (error) {
            console.error('Error loading market overview:', error);
            // Set empty fallback data
        }
    }, []);

    const loadIndicesData = useCallback(async () => {
        try {
            const indices = await xcapitalBackendService.getAvailableIndices();
            console.log('Available indices loaded:', indices);
            
            if (indices && indices.indices) {
                // Load detailed data for each index with values
                const enrichedIndices = await Promise.all(
                    indices.indices.map(async (index) => {
                        try {
                            // Get index values for the last month
                            const valuesResponse = await xcapitalBackendService.makeRequest(`/indices/${index.index_id}/values/?period=1M`);
                            
                            if (valuesResponse && valuesResponse.results && valuesResponse.results.length > 0) {
                                const latestValue = valuesResponse.results[0]; // Most recent value
                                const previousValue = valuesResponse.results[1] || latestValue;
                                
                                return {
                                    id: index.index_id,
                                    name: index.index_name,
                                    symbol: index.index_name,
                                    current_value: parseFloat(latestValue.value),
                                    previous_value: parseFloat(latestValue.previous_value || previousValue.value),
                                    daily_change: parseFloat(latestValue.daily_change || 0),
                                    daily_change_pct: parseFloat(latestValue.daily_change_pct || 0),
                                    last_update: latestValue.date_value,
                                    total_records: valuesResponse.count,
                                    raw_data: valuesResponse.results.slice(0, 5) // Keep some recent data
                                };
                            } else {
                                return {
                                    id: index.index_id,
                                    name: index.index_name,
                                    symbol: index.index_name,
                                    current_value: 0,
                                    previous_value: 0,
                                    daily_change: 0,
                                    daily_change_pct: 0,
                                    last_update: 'N/A',
                                    total_records: 0,
                                    raw_data: []
                                };
                            }
                        } catch (error) {
                            console.error(`Error loading values for ${index.index_name}:`, error);
                            return {
                                id: index.index_id,
                                name: index.index_name,
                                symbol: index.index_name,
                                current_value: 0,
                                previous_value: 0,
                                daily_change: 0,
                                daily_change_pct: 0,
                                last_update: 'Error',
                                total_records: 0,
                                raw_data: []
                            };
                        }
                    })
                );
                
                setIndicesData(enrichedIndices.map(index => cleanObjectForRendering(index)));
                console.log('Enriched indices data loaded:', enrichedIndices.length, 'indices');
            }
        } catch (error) {
            console.error('Error loading indices data:', error);
            setIndicesData([]);
        }
    }, []);

    const loadCompaniesData = useCallback(async () => {
        try {
            const companies = await xcapitalBackendService.getAvailableCompanies();
            const enrichedCompanies = await Promise.all(
                (Array.isArray(companies) ? companies : []).slice(0, 20).map(async (company) => {
                    try {
                        const variations = await xcapitalBackendService.getCompanyVariations(company.symbol);
                        return {
                            ...company,
                            variations: variations || {}
                        };
                    } catch (error) {
                        return company;
                    }
                })
            );
            
            setCompaniesData(enrichedCompanies.map(company => cleanObjectForRendering(company)));
            
        } catch (error) {
            console.error('Error loading companies:', error);
            setCompaniesData([]);
        }
    }, []);

    // Load Available Companies
    const loadAvailableCompanies = useCallback(async () => {
        try {
            console.log('Loading available companies...');
            const response = await xcapitalBackendService.getCompanies();
            
            if (response && response.success && Array.isArray(response.companies)) {
                const companies = response.companies;
                setAvailableCompanies(companies);
                console.log(`Loaded ${companies.length} companies:`, companies.slice(0, 3));
                
                // Set first company as default if none selected
                if (!selectedCompany && companies.length > 0) {
                    setSelectedCompany(companies[0].symbol);
                }
            } else {
                setAvailableCompanies([]);
                console.warn('No companies data received');
            }
        } catch (error) {
            console.error('Error loading companies:', error);
            setAvailableCompanies([]);
        }
    }, [selectedCompany]);

    // Load Market Statistics
    const loadMarketStatistics = useCallback(async () => {
        try {
            console.log('Loading market statistics...');
            const [
                totalCapData,
                avgVolumeData,
                topVolumeData,
                marketStatsData,
                sectorCompData,
                sectorPerfData
            ] = await Promise.all([
                xcapitalBackendService.getTotalMarketCapitalization(),
                xcapitalBackendService.getAvgDailyVolume(),
                xcapitalBackendService.getTopVolumeAnalysis(),
                xcapitalBackendService.getMarketStatistics(),
                xcapitalBackendService.getSectorComparison(),
                xcapitalBackendService.getSectorPerformance()
            ]);

            setTotalMarketCap(cleanObjectForRendering(totalCapData));
            setAvgDailyVolume(cleanObjectForRendering(avgVolumeData));
            
            // Safe handling of topVolumeData - ensure proper structure and clean any nested objects
            if (topVolumeData && typeof topVolumeData === 'object') {
                console.log('Top Volume Analysis structure:', topVolumeData);
                
                // Clean the data to remove any problematic objects
                const cleanedData = cleanObjectForRendering(topVolumeData);
                
                setTopVolumeAnalysis(cleanedData);
            } else {
                setTopVolumeAnalysis({});
            }
            
            // Ensure marketStatsData is properly processed and doesn't contain nested objects that could be rendered
            if (marketStatsData && typeof marketStatsData === 'object') {
                console.log('Market Stats Data Structure:', Object.keys(marketStatsData));
                console.log('Full marketStatsData:', marketStatsData);
                
                // Clean the data to remove any problematic objects
                const safeMarketStats = cleanObjectForRendering(marketStatsData);
                
                setMarketStatistics(safeMarketStats);
            } else {
                setMarketStatistics(null);
            }
            
            // Safe handling of sector data
            const safeSectorComparison = Array.isArray(sectorCompData.sector_comparison) ? sectorCompData.sector_comparison : [];
            const safeSectorPerformance = Array.isArray(sectorPerfData.sector_performance) ? sectorPerfData.sector_performance : [];
            
            // Clean each item in the arrays for nested objects that could cause rendering issues
            const cleanSectorComparison = safeSectorComparison.map(item => {
                return cleanObjectForRendering(item);
            });
            
            const cleanSectorPerformance = safeSectorPerformance.map(item => {
                return cleanObjectForRendering(item);
            });
            
            setSectorComparison(cleanSectorComparison);
            setSectorPerformance(cleanSectorPerformance);
            
            console.log('Market statistics loaded successfully');
            console.log('Sector comparison data:', cleanSectorComparison.slice(0, 1));
            console.log('Sector performance data:', cleanSectorPerformance.slice(0, 1));
        } catch (error) {
            console.error('Error loading market statistics:', error);
            toast({
                title: 'Error loading market statistics',
                description: error.message,
                status: 'error',
                duration: 3000,
            });
        }
    }, [toast]);

    // Load Index Values for Analysis
    const loadIndexValuesForAnalysis = useCallback(async () => {
        if (!selectedAnalysisIndex) return;
        
        console.log('DEBUG - Starting loadIndexValuesForAnalysis:', {
            selectedAnalysisIndex,
            analysisPeriod,
            customStartDate,
            customEndDate
        });
        
        setLoadingIndexValues(true);
        try {
            // Prepare request payload based on the new API format
            let requestData;
            
            if (analysisPeriod === 'CUSTOM' && customStartDate && customEndDate) {
                requestData = {
                    indices: [selectedAnalysisIndex],
                    period: "CUSTOM",
                    start_date: customStartDate,
                    end_date: customEndDate
                };
            } else {
                requestData = {
                    index_id: selectedAnalysisIndex,
                    period: analysisPeriod
                };
            }
            
            console.log('DEBUG - API Request payload:', requestData);
            
            // Use POST request as specified in the API
            const response = await xcapitalBackendService.makeRequest('/indices/form-data/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });
            
            console.log('DEBUG - API Response:', response);
            
            if (response && response.success && response.data) {
                // Transform the API data format to match the table structure
                const transformedData = response.data.map(item => ({
                    id: item.date, // Using date as ID since no ID provided in API
                    date: item.date,
                    value: parseFloat(item.value),
                    previous_value: parseFloat(item.previous_value || 0),
                    daily_change: parseFloat(item.daily_change || 0),
                    daily_change_pct: parseFloat(item.daily_change_pct || 0),
                    total_change: parseFloat(item.total_change || 0),
                    total_change_pct: parseFloat(item.total_change_pct || 0)
                }));
                
                console.log('DEBUG - Transformed data:', transformedData.slice(0, 3));
                
                setIndexValuesData(transformedData.map(item => cleanObjectForRendering(item)));
                console.log(`Loaded ${transformedData.length} values for index ${selectedAnalysisIndex}`, {
                    summary: response.summary,
                    period: analysisPeriod,
                    dateRange: response.summary ? {
                        from: response.summary.start_date,
                        to: response.summary.end_date,
                        total_change: response.summary.total_change,
                        total_change_pct: response.summary.total_change_pct
                    } : null,
                    sample: transformedData.slice(0, 3)
                });
            } else {
                setIndexValuesData([]);
                console.warn('DEBUG - No index values data received or API returned error:', response);
            }
        } catch (error) {
            console.error('DEBUG - Error loading index values:', error);
            setIndexValuesData([]);
        } finally {
            setLoadingIndexValues(false);
        }
    }, [selectedAnalysisIndex, analysisPeriod, customStartDate, customEndDate]);

    // Load index values when analysis parameters change
    useEffect(() => {
        if (selectedAnalysisIndex && analysisPeriod !== 'CUSTOM') {
            loadIndexValuesForAnalysis();
        } else if (selectedAnalysisIndex && analysisPeriod === 'CUSTOM' && customStartDate && customEndDate) {
            loadIndexValuesForAnalysis();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedAnalysisIndex, analysisPeriod, customStartDate, customEndDate]);

    // Initial load when component mounts and indices data is available
    useEffect(() => {
        console.log('Debug - Initial load effect triggered:', {
            indicesDataLength: indicesData.length,
            selectedAnalysisIndex,
            indexValuesDataLength: indexValuesData.length,
            loadingIndexValues,
            analysisPeriod
        });
        
        if (indicesData.length > 0 && selectedAnalysisIndex && !indexValuesData.length && !loadingIndexValues) {
            console.log('Initial load triggered for index:', selectedAnalysisIndex);
            loadIndexValuesForAnalysis();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [indicesData, selectedAnalysisIndex]);

    // Load Company Chart Data
    const loadCompanyData = useCallback(async () => {
        if (!selectedCompany) return;
        
        setLoadingCompanyData(true);
        try {
            console.log(`Loading company data for ${selectedCompany}, period: ${companyPeriod}`);
            
            // Load comprehensive company data using the secure endpoint
            console.log(`Making API call for company data:`, {
                symbol: selectedCompany,
                period: companyPeriod
            });
            
            const companyDataResponse = await xcapitalBackendService.getCompanyData(selectedCompany, companyPeriod);
            console.log('Company data API response:', companyDataResponse);
            
            if (companyDataResponse && companyDataResponse.success) {
                // Set company details
                setCompanyDetails(companyDataResponse.company_info);
                
                let transformedChartData = [];
                
                // Transform chart data from data array
                if (companyDataResponse.data && Array.isArray(companyDataResponse.data)) {
                    transformedChartData = companyDataResponse.data.map(item => ({
                        date: item.date,
                        price: parseFloat(item.close_price || 0),
                        volume: parseInt(item.volume || 0),
                        high: parseFloat(item.high_price || 0),
                        low: parseFloat(item.low_price || 0),
                        open: parseFloat(item.open_price || 0),
                        current_price: parseFloat(item.current_price || item.close_price || 0),
                        shares_traded: parseInt(item.shares_traded || 0),
                        total_trades: parseInt(item.total_trades || 0),
                        market_cap: parseFloat(item.market_cap || 0)
                    }));
                    
                    // Sort by date to ensure proper chronological order
                    transformedChartData.sort((a, b) => new Date(a.date) - new Date(b.date));
                    
                    // Filter data based on period if API returns all data
                    if (companyPeriod !== 'ALL' && companyPeriod !== 'CUSTOM') {
                        const today = new Date();
                        let cutoffDate = new Date(today);
                        
                        switch (companyPeriod) {
                            case '1M':
                                cutoffDate.setMonth(cutoffDate.getMonth() - 1);
                                break;
                            case '3M':
                                cutoffDate.setMonth(cutoffDate.getMonth() - 3);
                                break;
                            case '6M':
                                cutoffDate.setMonth(cutoffDate.getMonth() - 6);
                                break;
                            case '1Y':
                                cutoffDate.setFullYear(cutoffDate.getFullYear() - 1);
                                break;
                            case '2Y':
                                cutoffDate.setFullYear(cutoffDate.getFullYear() - 2);
                                break;
                            case '3Y':
                                cutoffDate.setFullYear(cutoffDate.getFullYear() - 3);
                                break;
                            default:
                                // For any unknown period, show all data
                                cutoffDate = new Date('2022-08-22');
                                break;
                        }
                        
                        transformedChartData = transformedChartData.filter(item => 
                            new Date(item.date) >= cutoffDate
                        );
                        
                        console.log(`📅 Filtered data for ${companyPeriod} period:`, {
                            cutoffDate: cutoffDate.toISOString().split('T')[0],
                            originalLength: companyDataResponse.data.length,
                            filteredLength: transformedChartData.length
                        });
                    }
                    
                    setCompanyChartData(transformedChartData);
                    console.log(`Loaded ${transformedChartData.length} chart data points for ${selectedCompany}:`, {
                        firstDate: transformedChartData[0]?.date,
                        lastDate: transformedChartData[transformedChartData.length - 1]?.date,
                        sampleData: transformedChartData.slice(0, 3)
                    });
                } else {
                    setCompanyChartData([]);
                    console.warn('No data array in response for', selectedCompany);
                }
                
                // Set raw prices data for table display (filtered to match the selected period)
                let filteredTableData = companyDataResponse.data || [];
                
                // Apply the same period filtering to table data as we do for charts
                if (companyPeriod !== 'ALL' && companyPeriod !== 'CUSTOM' && filteredTableData.length > 0) {
                    const today = new Date();
                    let cutoffDate = new Date(today);
                    
                    switch (companyPeriod) {
                        case '1M':
                            cutoffDate.setMonth(cutoffDate.getMonth() - 1);
                            break;
                        case '3M':
                            cutoffDate.setMonth(cutoffDate.getMonth() - 3);
                            break;
                        case '6M':
                            cutoffDate.setMonth(cutoffDate.getMonth() - 6);
                            break;
                        case '1Y':
                            cutoffDate.setFullYear(cutoffDate.getFullYear() - 1);
                            break;
                        case '2Y':
                            cutoffDate.setFullYear(cutoffDate.getFullYear() - 2);
                            break;
                        case '3Y':
                            cutoffDate.setFullYear(cutoffDate.getFullYear() - 3);
                            break;
                        default:
                            // For any unknown period, show all data
                            cutoffDate = new Date('2022-08-22');
                            break;
                    }
                    
                    filteredTableData = filteredTableData.filter(item => 
                        new Date(item.date) >= cutoffDate
                    );
                    
                    console.log(`📅 Table data filtered for ${companyPeriod} period:`, {
                        cutoffDate: cutoffDate.toISOString().split('T')[0],
                        originalTableLength: companyDataResponse.data.length,
                        filteredTableLength: filteredTableData.length
                    });
                }
                
                setCompanyPricesData(filteredTableData);
                
                console.log('Company data loaded successfully:', {
                    company: companyDataResponse.company_info,
                    period: companyDataResponse.period,
                    dataPoints: companyDataResponse.data?.length || 0,
                    timestamp: companyDataResponse.timestamp
                });
            } else {
                console.warn('Invalid company data response for', selectedCompany, companyDataResponse);
                setCompanyChartData([]);
                setCompanyPricesData([]);
                setCompanyDetails(null);
            }
            
        } catch (error) {
            console.error('Error loading company data:', error);
            setCompanyChartData([]);
            setCompanyPricesData([]);
            setCompanyDetails(null);
        } finally {
            setLoadingCompanyData(false);
        }
    }, [selectedCompany, companyPeriod]);

    // Auto-reload company data when selection changes
    useEffect(() => {
        if (selectedCompany && companyPeriod) {
            loadCompanyData();
        }
    }, [selectedCompany, companyPeriod, loadCompanyData]);

    // Refresh Function
    const handleRefresh = useCallback(async () => {
        setRefreshing(true);
        try {
            await Promise.all([
                loadMarketOverview(),
                loadIndicesData(),
                loadCompaniesData(),
                loadAvailableCompanies(),
                loadMarketStatistics()
            ]);
            toast({
                title: "Data Refreshed",
                description: "All market data has been updated",
                status: "success",
                duration: 3000,
                isClosable: true,
            });
        } catch (error) {
            console.error('Error refreshing data:', error);
            toast({
                title: "Refresh Failed",
                description: "Some data could not be refreshed. Please try again.",
                status: "error",
                duration: 5000,
                isClosable: true,
            });
        }
        setRefreshing(false);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [toast]);

    // Effects - Load data once on mount
    useEffect(() => {
        const loadInitialData = async () => {
            setIsLoading(true);
            try {
                await Promise.all([
                    loadMarketOverview(),
                    loadIndicesData(),
                    loadCompaniesData(),
                    loadAvailableCompanies(),
                    loadMarketStatistics()
                ]);
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                toast({
                    title: "Data Loading Error",
                    description: "Some data could not be loaded. Please try refreshing.",
                    status: "warning",
                    duration: 5000,
                    isClosable: true,
                });
            } finally {
                setIsLoading(false);
            }
        };
        
        loadInitialData();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []); // Only run on mount

    // Real-time updates
    useEffect(() => {
        let interval;
        if (realTimeMode) {
            interval = setInterval(() => {
                handleRefresh();
            }, 30000); // Refresh every 30 seconds
        }
        return () => {
            if (interval) clearInterval(interval);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [realTimeMode]); // Only depend on realTimeMode, not handleRefresh

    // Professional Table Component with Full-Width Responsive Design
    const ProfessionalTable = ({ title, data, columns, maxHeight = "400px" }) => (
        <MotionCard
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            whileHover={{ 
                boxShadow: "0 20px 40px rgba(255, 206, 48, 0.1)",
                transform: "translateY(-2px)"
            }}
            bg={cardBg}
            borderWidth="1px"
            borderColor={borderColor}
            borderRadius="xl"
            overflow="hidden"
            w="100%"
            position="relative"
            _before={{
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '3px',
                bg: `linear-gradient(90deg, ${accentColor} 0%, rgba(255, 206, 48, 0.3) 100%)`,
                borderTopRadius: 'xl'
            }}
        >
            <Box 
                p={{ base: 4, md: 6 }} 
                borderBottomWidth="1px" 
                borderColor={borderColor}
                bg="rgba(255, 206, 48, 0.05)"
            >
                <Heading size={{ base: "sm", md: "md" }} color={accentColor} fontWeight="600">
                    {title}
                </Heading>
            </Box>
            <TableContainer 
                maxHeight={maxHeight} 
                overflowY="auto"
                sx={{
                    '&::-webkit-scrollbar': {
                        width: '8px',
                    },
                    '&::-webkit-scrollbar-track': {
                        bg: 'rgba(255,255,255,0.1)',
                        borderRadius: '4px',
                    },
                    '&::-webkit-scrollbar-thumb': {
                        bg: accentColor,
                        borderRadius: '4px',
                    },
                }}
            >
                <Table variant="simple" size="sm">
                    <Thead bg="rgba(51, 51, 51, 0.8)" position="sticky" top={0} zIndex={10}>
                        <Tr>
                            {columns.map((col, index) => (
                                <Th 
                                    key={index} 
                                    fontWeight="bold" 
                                    fontSize="xs" 
                                    textTransform="uppercase" 
                                    color="rgba(255,255,255,0.8)"
                                    py={4}
                                    borderColor={borderColor}
                                    letterSpacing="0.5px"
                                >
                                    {col.header}
                                </Th>
                            ))}
                        </Tr>
                    </Thead>
                    <Tbody>
                        {data.map((row, index) => (
                            <Tr 
                                key={index} 
                                _hover={{ 
                                    bg: "rgba(255, 206, 48, 0.1)",
                                    transform: "scale(1.01)",
                                    transition: "all 0.2s ease"
                                }}
                                transition="all 0.2s ease"
                            >
                                {columns.map((col, colIndex) => (
                                    <Td 
                                        key={colIndex} 
                                        py={4} 
                                        color="#ffffff"
                                        borderColor={borderColor}
                                        fontSize="sm"
                                    >
                                        {col.render ? col.render(row[col.key], row) : (
                                            typeof row[col.key] === 'object' && row[col.key] !== null ? 
                                                JSON.stringify(row[col.key]) : 
                                                (row[col.key] ?? 'N/A')
                                        )}
                                    </Td>
                                ))}
                            </Tr>
                        ))}
                    </Tbody>
                </Table>
            </TableContainer>
        </MotionCard>
    );

    if (isLoading) {
        return (
            <MotionBox 
                minH="100vh" 
                bg={bgGradient} 
                color="#ffffff"
                w="100%"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
            >
                <Box 
                    w="100%" 
                    maxW="none"
                    px={{ base: 4, md: 6, lg: 8 }}
                    py={6}
                >
                    <VStack spacing={6}>
                        <Skeleton height="80px" borderRadius="xl" bg="rgba(255,255,255,0.1)" />
                        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} w="full">
                            {[1, 2, 3, 4].map(i => (
                                <Skeleton key={i} height="120px" borderRadius="xl" bg="rgba(255,255,255,0.1)" />
                            ))}
                        </SimpleGrid>
                        <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6} w="full">
                            <Skeleton height="400px" borderRadius="xl" bg="rgba(255,255,255,0.1)" />
                            <Skeleton height="400px" borderRadius="xl" bg="rgba(255,255,255,0.1)" />
                        </SimpleGrid>
                    </VStack>
                </Box>
            </MotionBox>
        );
    }

    return (
        <MotionBox 
            minH="100vh" 
            bg={bgGradient} 
            color="#ffffff"
            w="100%"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
        >
            {/* Full-width professional container */}
            <Box 
                w="100%" 
                maxW="none"
                px={{ base: 4, md: 6, lg: 8 }}
                py={6}
            >
                {/* Header */}
                {/* Stock Ticker replacing header */}
                <MotionBox
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.4, delay: 0.1 }}
                >
                    <StockTicker />
                </MotionBox>

                {/* Main Content Tabs with Professional Design */}
                <MotionBox
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                >
                    <Tabs index={activeTab} onChange={setActiveTab} variant="unstyled" colorScheme="yellow">
                    <TabList 
                        mb={8} 
                        mt={6}
                        bg="rgba(26,32,44,0.6)"
                        borderRadius="lg" 
                        p={0}
                        border="1px solid rgba(255,255,255,0.1)"
                        height="50px"
                        alignItems="center"
                        position="relative"
                    >
                        <Tab 
                            fontWeight="500" 
                            color="rgba(255,255,255,0.7)" 
                            px={6}
                            py={2}
                            height="100%"
                            transition="all 0.2s ease"
                            position="relative"
                            fontSize="sm"
                            _selected={{ 
                                color: "#FFD700",
                                fontWeight: "600",
                                _after: {
                                    content: '""',
                                    position: 'absolute',
                                    bottom: 0,
                                    left: '50%',
                                    transform: 'translateX(-50%)',
                                    width: '80%',
                                    height: '2px',
                                    bg: 'linear-gradient(90deg, #FFD700, #FFA500)',
                                    borderRadius: '1px'
                                }
                            }} 
                            _hover={{ 
                                color: "#ffffff",
                                _after: {
                                    content: '""',
                                    position: 'absolute',
                                    bottom: 0,
                                    left: '50%',
                                    transform: 'translateX(-50%)',
                                    width: '60%',
                                    height: '1px',
                                    bg: 'rgba(255,255,255,0.3)',
                                    borderRadius: '1px'
                                }
                            }}
                        >
                            <HStack spacing={2}>
                                <FiTrendingUp size={16} />
                                <Text>Market View</Text>
                            </HStack>
                        </Tab>
                        <Tab 
                            fontWeight="500" 
                            color="rgba(255,255,255,0.7)" 
                            px={6}
                            py={2}
                            height="100%"
                            transition="all 0.2s ease"
                            position="relative"
                            fontSize="sm"
                            _selected={{ 
                                color: "#FFD700",
                                fontWeight: "600",
                                _after: {
                                    content: '""',
                                    position: 'absolute',
                                    bottom: 0,
                                    left: '50%',
                                    transform: 'translateX(-50%)',
                                    width: '80%',
                                    height: '2px',
                                    bg: 'linear-gradient(90deg, #FFD700, #FFA500)',
                                    borderRadius: '1px'
                                }
                            }} 
                            _hover={{ 
                                color: "#ffffff",
                                _after: {
                                    content: '""',
                                    position: 'absolute',
                                    bottom: 0,
                                    left: '50%',
                                    transform: 'translateX(-50%)',
                                    width: '60%',
                                    height: '1px',
                                    bg: 'rgba(255,255,255,0.3)',
                                    borderRadius: '1px'
                                }
                            }}
                        >
                            <HStack spacing={2}>
                                <FiBarChart2 size={16} />
                                <Text>Indices Analysis</Text>
                            </HStack>
                        </Tab>
                        <Tab 
                            fontWeight="500" 
                            color="rgba(255,255,255,0.7)" 
                            px={6}
                            py={2}
                            height="100%"
                            transition="all 0.2s ease"
                            position="relative"
                            fontSize="sm"
                            _selected={{ 
                                color: "#FFD700",
                                fontWeight: "600",
                                _after: {
                                    content: '""',
                                    position: 'absolute',
                                    bottom: 0,
                                    left: '50%',
                                    transform: 'translateX(-50%)',
                                    width: '80%',
                                    height: '2px',
                                    bg: 'linear-gradient(90deg, #FFD700, #FFA500)',
                                    borderRadius: '1px'
                                }
                            }} 
                            _hover={{ 
                                color: "#ffffff",
                                _after: {
                                    content: '""',
                                    position: 'absolute',
                                    bottom: 0,
                                    left: '50%',
                                    transform: 'translateX(-50%)',
                                    width: '60%',
                                    height: '1px',
                                    bg: 'rgba(255,255,255,0.3)',
                                    borderRadius: '1px'
                                }
                            }}
                        >
                            <HStack spacing={2}>
                                <FiGlobe size={16} />
                                <Text>Companies Data</Text>
                            </HStack>
                        </Tab>
                        <Tab 
                            fontWeight="500" 
                            color="rgba(255,255,255,0.7)" 
                            px={6}
                            py={2}
                            height="100%"
                            transition="all 0.2s ease"
                            position="relative"
                            fontSize="sm"
                            _selected={{ 
                                color: "#FFD700",
                                fontWeight: "600",
                                _after: {
                                    content: '""',
                                    position: 'absolute',
                                    bottom: 0,
                                    left: '50%',
                                    transform: 'translateX(-50%)',
                                    width: '80%',
                                    height: '2px',
                                    bg: 'linear-gradient(90deg, #FFD700, #FFA500)',
                                    borderRadius: '1px'
                                }
                            }} 
                            _hover={{ 
                                color: "#ffffff",
                                _after: {
                                    content: '""',
                                    position: 'absolute',
                                    bottom: 0,
                                    left: '50%',
                                    transform: 'translateX(-50%)',
                                    width: '60%',
                                    height: '1px',
                                    bg: 'rgba(255,255,255,0.3)',
                                    borderRadius: '1px'
                                }
                            }}
                        >
                            <HStack spacing={2}>
                                <FiTarget size={16} />
                                <Text>Performance</Text>
                            </HStack>
                        </Tab>
                    </TabList>

                    <TabPanels>
                        {/* Market View Tab */}
                        <TabPanel p={0}>
                            <VStack spacing={6}>
                                {/* Market Statistics Overview */}
                                <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6} w="full">
                                    {/* Total Market Capitalization */}
                                    <MotionCard
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.5 }}
                                        whileHover={{ 
                                            boxShadow: "0 20px 40px rgba(255, 206, 48, 0.1)",
                                            transform: "translateY(-2px)"
                                        }}
                                        bg={cardBg}
                                        borderWidth="1px"
                                        borderColor={borderColor}
                                        borderRadius="xl"
                                        overflow="hidden"
                                        position="relative"
                                        _before={{
                                            content: '""',
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            right: 0,
                                            height: '3px',
                                            background: 'linear-gradient(90deg, #FFD700, #FFA500)'
                                        }}
                                    >
                                        <CardBody>
                                            <VStack align="start" spacing={3}>
                                                <HStack justify="space-between" w="full">
                                                    <Text fontSize="sm" color="gray.400" fontWeight="medium">
                                                        Total Market Capitalization
                                                    </Text>
                                                    <Icon as={FiTrendingUp} color={accentColor} />
                                                </HStack>
                                                <Text fontSize="2xl" fontWeight="bold" color="#ffffff">
                                                    {totalMarketCap ? `${totalMarketCap.formatted_total_cap}` : 'Loading...'}
                                                </Text>
                                                <Text fontSize="sm" color="gray.500">
                                                    {totalMarketCap ? `${totalMarketCap.companies_count} companies` : ''}
                                                </Text>
                                            </VStack>
                                        </CardBody>
                                    </MotionCard>

                                    {/* Average Daily Volume */}
                                    <MotionCard
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.5, delay: 0.1 }}
                                        whileHover={{ 
                                            boxShadow: "0 20px 40px rgba(255, 206, 48, 0.1)",
                                            transform: "translateY(-2px)"
                                        }}
                                        bg={cardBg}
                                        borderWidth="1px"
                                        borderColor={borderColor}
                                        borderRadius="xl"
                                        overflow="hidden"
                                        position="relative"
                                        _before={{
                                            content: '""',
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            right: 0,
                                            height: '3px',
                                            background: 'linear-gradient(90deg, #00D4FF, #0099CC)'
                                        }}
                                    >
                                        <CardBody>
                                            <VStack align="start" spacing={3}>
                                                <HStack justify="space-between" w="full">
                                                    <Text fontSize="sm" color="gray.400" fontWeight="medium">
                                                        Average Daily Volume
                                                    </Text>
                                                    <Icon as={FiActivity} color="blue.400" />
                                                </HStack>
                                                <Text fontSize="2xl" fontWeight="bold" color="#ffffff">
                                                    {avgDailyVolume ? `${avgDailyVolume.formatted_avg_volume}` : 'Loading...'}
                                                </Text>
                                                <Text fontSize="sm" color="gray.500">
                                                    {avgDailyVolume ? `Across ${avgDailyVolume.companies_count} companies` : ''}
                                                </Text>
                                            </VStack>
                                        </CardBody>
                                    </MotionCard>

                                    {/* Top Volume Analysis - Curve Format */}
                                    <MotionCard
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.5, delay: 0.2 }}
                                        whileHover={{ 
                                            boxShadow: "0 20px 40px rgba(255, 206, 48, 0.1)",
                                            transform: "translateY(-2px)"
                                        }}
                                        bg={cardBg}
                                        borderWidth="1px"
                                        borderColor={borderColor}
                                        borderRadius="xl"
                                        overflow="hidden"
                                        position="relative"
                                        _before={{
                                            content: '""',
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            right: 0,
                                            height: '3px',
                                            background: 'linear-gradient(90deg, #FF6B6B, #EE5A52)'
                                        }}
                                    >
                                        <CardBody>
                                            <VStack align="start" spacing={3}>
                                                <HStack justify="space-between" w="full">
                                                    <Text fontSize="sm" color="gray.400" fontWeight="medium">
                                                        Top Volume Today
                                                    </Text>
                                                    <Icon as={FiBarChart} color="red.400" />
                                                </HStack>
                                                {topVolumeAnalysis && topVolumeAnalysis.top_companies && Array.isArray(topVolumeAnalysis.top_companies) ? (
                                                    <VStack align="start" spacing={2} w="full">
                                                        {topVolumeAnalysis.top_companies.slice(0, 3).map((company, index) => (
                                                            <HStack key={company.company_name || index} justify="space-between" w="full">
                                                                <Text fontSize="sm" color="#ffffff" noOfLines={1}>
                                                                    <SafeDataRenderer data={company.company_name} fallback={`Company ${index + 1}`} />
                                                                </Text>
                                                                <Text fontSize="sm" color={accentColor} fontWeight="bold">
                                                                    <SafeDataRenderer data={company.formatted_volume} fallback="N/A" />
                                                                </Text>
                                                            </HStack>
                                                        ))}
                                                    </VStack>
                                                ) : (
                                                    <Text fontSize="sm" color="gray.500">Loading...</Text>
                                                )}
                                            </VStack>
                                        </CardBody>
                                    </MotionCard>
                                </SimpleGrid>

                                {/* Professional Market Overview */}
                                <ProfessionalMarketOverview />
                            </VStack>
                        </TabPanel>

                        {/* Indices Analysis Tab */}
                        <TabPanel p={0}>
                            <VStack spacing={6}>
                                {/* Index Selection and Controls */}
                                <Card w="full" bg={cardBg} shadow="lg" borderRadius="xl">
                                    <CardBody>
                                        <VStack spacing={4}>
                                            <HStack w="full" justify="space-between" align="center">
                                                <VStack align="start" spacing={1}>
                                                    <Heading size="md" color={accentColor}>Index Analysis</Heading>
                                                    <Text fontSize="xs" color="gray.500">
                                                        Historical data available from 22/08/2022 onwards
                                                    </Text>
                                                </VStack>
                                                <Button
                                                    size="sm"
                                                    colorScheme="blue"
                                                    variant="outline"
                                                    onClick={loadIndexValuesForAnalysis}
                                                    isLoading={loadingIndexValues}
                                                    leftIcon={<FiRefreshCw />}
                                                >
                                                    Refresh Data
                                                </Button>
                                            </HStack>
                                            
                                            <SimpleGrid columns={{ base: 1, md: 4 }} spacing={4} w="full">
                                                {/* Index Selection with dark theme */}
                                                <FormControl>
                                                    <FormLabel fontSize="sm" color="#ffffff">Select Index</FormLabel>
                                                    <Select
                                                        value={selectedAnalysisIndex}
                                                        onChange={(e) => {
                                                            console.log('DEBUG - Index selection changed:', e.target.value);
                                                            setSelectedAnalysisIndex(e.target.value);
                                                        }}
                                                        size="sm"
                                                        bg={inputBg}
                                                        borderColor={borderColor}
                                                        color="#ffffff"
                                                        _hover={{ borderColor: accentColor }}
                                                        _focus={{ borderColor: accentColor, boxShadow: `0 0 0 1px ${accentColor}` }}
                                                    >
                                                        {indicesData.map(index => {
                                                            console.log('DEBUG - Rendering index option:', index);
                                                            return (
                                                                <option key={index.id} value={index.id} style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>
                                                                    {index.name}
                                                                </option>
                                                            );
                                                        })}
                                                    </Select>
                                                </FormControl>
                                                
                                                {/* Period Selection with dark theme */}
                                                <FormControl>
                                                    <FormLabel fontSize="sm" color="#ffffff">Time Period</FormLabel>
                                                    <Select
                                                        value={analysisPeriod}
                                                        onChange={(e) => setAnalysisPeriod(e.target.value)}
                                                        size="sm"
                                                        bg={inputBg}
                                                        borderColor={borderColor}
                                                        color="#ffffff"
                                                        _hover={{ borderColor: accentColor }}
                                                        _focus={{ borderColor: accentColor, boxShadow: `0 0 0 1px ${accentColor}` }}
                                                    >
                                                        <option value="1M" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Last Month</option>
                                                        <option value="3M" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Last 3 Months</option>
                                                        <option value="6M" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Last 6 Months</option>
                                                        <option value="1Y" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Last Year</option>
                                                        <option value="2Y" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Last 2 Years</option>
                                                        <option value="3Y" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Last 3 Years</option>
                                                        <option value="ALL" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>All Data (Since 22/08/2022)</option>
                                                        <option value="CUSTOM" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Custom Date Range</option>
                                                    </Select>
                                                </FormControl>
                                                
                                                {/* Custom Start Date with dark theme */}
                                                {analysisPeriod === 'CUSTOM' && (
                                                    <FormControl>
                                                        <FormLabel fontSize="sm" color="#ffffff">Start Date</FormLabel>
                                                        <Input
                                                            type="date"
                                                            value={customStartDate}
                                                            onChange={(e) => setCustomStartDate(e.target.value)}
                                                            size="sm"
                                                            min="2022-08-22"
                                                            max={customEndDate || new Date().toISOString().split('T')[0]}
                                                            bg={inputBg}
                                                            borderColor={borderColor}
                                                            color="#ffffff"
                                                            _hover={{ borderColor: accentColor }}
                                                            _focus={{ borderColor: accentColor, boxShadow: `0 0 0 1px ${accentColor}` }}
                                                        />
                                                    </FormControl>
                                                )}
                                                
                                                {/* Custom End Date with dark theme */}
                                                {analysisPeriod === 'CUSTOM' && (
                                                    <FormControl>
                                                        <FormLabel fontSize="sm" color="#ffffff">End Date</FormLabel>
                                                        <Input
                                                            type="date"
                                                            value={customEndDate}
                                                            onChange={(e) => setCustomEndDate(e.target.value)}
                                                            size="sm"
                                                            min={customStartDate || "2022-08-22"}
                                                            max={new Date().toISOString().split('T')[0]}
                                                            bg={inputBg}
                                                            borderColor={borderColor}
                                                            color="#ffffff"
                                                            _hover={{ borderColor: accentColor }}
                                                            _focus={{ borderColor: accentColor, boxShadow: `0 0 0 1px ${accentColor}` }}
                                                        />
                                                    </FormControl>
                                                )}
                                            </SimpleGrid>
                                        </VStack>
                                    </CardBody>
                                </Card>

                                {/* Index Values Chart */}
                                {loadingIndexValues ? (
                                    <Card w="full" bg={cardBg} shadow="lg" borderRadius="xl">
                                        <CardBody>
                                            <Center h="400px">
                                                <VStack>
                                                    <Spinner size="lg" color={accentColor} />
                                                    <Text color="#888">Loading index data...</Text>
                                                </VStack>
                                            </Center>
                                        </CardBody>
                                    </Card>
                                ) : indexValuesData.length > 0 ? (
                                    <Card w="full" bg={cardBg} shadow="lg" borderRadius="xl">
                                        <CardBody>
                                            <VStack spacing={4}>
                                                <HStack w="full" justify="space-between">
                                                    <VStack align="start" spacing={1}>
                                                        <Heading size="md" color={accentColor}>
                                                            {indexValuesData[0]?.index_name} Performance
                                                            {analysisPeriod === 'ALL' && ' (Since 22/08/2022)'}
                                                            {analysisPeriod === 'CUSTOM' && customStartDate && customEndDate && 
                                                                ` (${customStartDate} to ${customEndDate})`}
                                                            {!['ALL', 'CUSTOM'].includes(analysisPeriod) && ` (${analysisPeriod})`}
                                                        </Heading>
                                                        <Text fontSize="sm" color="gray.500">
                                                            {indexValuesData.length} data points
                                                            {indexValuesData.length > 0 && (
                                                                ` • ${indexValuesData[indexValuesData.length - 1]?.date} to ${indexValuesData[0]?.date}`
                                                            )}
                                                        </Text>
                                                    </VStack>
                                                </HStack>
                                                
                                                <Box w="full" height="400px">
                                                    <ResponsiveContainer width="100%" height={350} minWidth={300} minHeight={300}>
                                                        <ComposedChart data={[...indexValuesData].reverse()}>
                                                            <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                                                            <XAxis 
                                                                dataKey="date" 
                                                                fontSize={12}
                                                                stroke="#718096"
                                                                tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                                                            />
                                                            <YAxis 
                                                                fontSize={12}
                                                                stroke="#718096"
                                                                domain={['dataMin - 100', 'dataMax + 100']}
                                                            />
                                                            <Tooltip 
                                                                contentStyle={{
                                                                    backgroundColor: cardBg,
                                                                    border: `1px solid ${borderColor}`,
                                                                    borderRadius: '8px'
                                                                }}
                                                                formatter={(value, name) => [parseFloat(value).toLocaleString(), name]}
                                                                labelFormatter={(label) => new Date(label).toLocaleDateString()}
                                                            />
                                                            <Legend />
                                                            <Area
                                                                dataKey="value"
                                                                stroke={accentColor}
                                                                fill={`url(#colorIndexValue)`}
                                                                strokeWidth={2}
                                                                name="Index Value"
                                                            />
                                                            <Bar 
                                                                dataKey="daily_change" 
                                                                fill="#60a5fa" 
                                                                opacity={0.6}
                                                                name="Daily Change"
                                                                yAxisId="right"
                                                            />
                                                            <YAxis yAxisId="right" orientation="right" fontSize={12} stroke="#718096" />
                                                            <defs>
                                                                <linearGradient id="colorIndexValue" x1="0" y1="0" x2="0" y2="1">
                                                                    <stop offset="5%" stopColor={accentColor} stopOpacity={0.3}/>
                                                                    <stop offset="95%" stopColor={accentColor} stopOpacity={0}/>
                                                                </linearGradient>
                                                            </defs>
                                                        </ComposedChart>
                                                    </ResponsiveContainer>
                                                </Box>
                                            </VStack>
                                        </CardBody>
                                    </Card>
                                ) : selectedAnalysisIndex ? (
                                    <Card w="full" bg={cardBg} shadow="lg" borderRadius="xl">
                                        <CardBody>
                                            <Center h="400px">
                                                <VStack>
                                                    <Text color="#888" fontSize="lg">No data available</Text>
                                                    <Text color="gray.400" fontSize="sm">
                                                        Try clicking "Refresh Data" or select a different period
                                                    </Text>
                                                    <Button 
                                                        mt={4}
                                                        colorScheme="blue" 
                                                        variant="outline"
                                                        onClick={loadIndexValuesForAnalysis}
                                                        leftIcon={<FiRefreshCw />}
                                                    >
                                                        Refresh Data
                                                    </Button>
                                                </VStack>
                                            </Center>
                                        </CardBody>
                                    </Card>
                                ) : (
                                    <Card w="full" bg={cardBg} shadow="lg" borderRadius="xl">
                                        <CardBody>
                                            <Center h="400px">
                                                <VStack>
                                                    <Text color="#888" fontSize="lg">Select an index to view chart</Text>
                                                    <Text color="gray.400" fontSize="sm">Choose an index from the dropdown above</Text>
                                                </VStack>
                                            </Center>
                                        </CardBody>
                                    </Card>
                                )}

                                {/* Selected Index Data Table */}
                                {selectedAnalysisIndex && indexValuesData.length > 0 && (
                                    <Card w="full" bg={cardBg} shadow="lg" borderRadius="xl">
                                        <CardBody>
                                            <ProfessionalTable
                                                title={
                                                    <HStack spacing={2}>
                                                        <MdAnalytics />
                                                        <Text>{selectedAnalysisIndex} Historical Data ({analysisPeriod})</Text>
                                                    </HStack>
                                                }
                                                data={indexValuesData.slice().reverse().slice(0, 100)} // Show latest 100 records in reverse chronological order
                                                columns={[
                                                    { 
                                                        key: 'date', 
                                                        header: 'Date',
                                                        render: (value) => new Date(value).toLocaleDateString()
                                                    },
                                                    { 
                                                        key: 'value', 
                                                        header: 'Index Value',
                                                        render: (value) => (
                                                            <Text fontWeight="bold" color={accentColor}>
                                                                {parseFloat(value || 0).toLocaleString()}
                                                            </Text>
                                                        )
                                                    },
                                                    { 
                                                        key: 'daily_change', 
                                                        header: 'Daily Change',
                                                        render: (value) => {
                                                            const change = parseFloat(value || 0);
                                                            return (
                                                                <Text color={change >= 0 ? successColor : errorColor}>
                                                                    {change > 0 ? '+' : ''}{change.toFixed(2)}
                                                                </Text>
                                                            );
                                                        }
                                                    },
                                                    { 
                                                        key: 'daily_change_pct', 
                                                        header: 'Change %',
                                                        render: (value) => {
                                                            const changePct = parseFloat(value || 0);
                                                            return (
                                                                <HStack>
                                                                    <Text fontSize="sm" color={changePct >= 0 ? successColor : errorColor}>
                                                                        {changePct >= 0 ? '▲' : '▼'}
                                                                    </Text>
                                                                    <Text 
                                                                        color={changePct >= 0 ? successColor : errorColor}
                                                                        fontWeight="bold"
                                                                    >
                                                                        {Math.abs(changePct).toFixed(2)}%
                                                                    </Text>
                                                                </HStack>
                                                            );
                                                        }
                                                    }
                                                ]}
                                                maxHeight="400px"
                                            />
                                        </CardBody>
                                    </Card>
                                )}

                                {/* Sector Analysis Subsection */}
                                <VStack spacing={6} w="full">
                                    {/* Sector Comparison - Table/Cards Format */}
                                    <MotionCard
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.5 }}
                                        whileHover={{ 
                                            boxShadow: "0 20px 40px rgba(255, 206, 48, 0.1)",
                                            transform: "translateY(-2px)"
                                        }}
                                        bg={cardBg}
                                        borderWidth="1px"
                                        borderColor={borderColor}
                                        borderRadius="xl"
                                        overflow="hidden"
                                        w="100%"
                                        position="relative"
                                        _before={{
                                            content: '""',
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            right: 0,
                                            height: '3px',
                                            background: 'linear-gradient(90deg, #8B5CF6, #A855F7)'
                                        }}
                                    >
                                        <CardHeader>
                                            <HStack justify="space-between" align="center">
                                                <VStack align="start" spacing={1}>
                                                    <Heading size="md" color={accentColor}>Sector Comparison</Heading>
                                                    <Text fontSize="sm" color="gray.500">
                                                        Compare sector performance metrics
                                                    </Text>
                                                </VStack>
                                                <Icon as={FiBarChart} color="purple.400" fontSize="24px" />
                                            </HStack>
                                        </CardHeader>
                                        <CardBody pt={0}>
                                            {sectorComparison && sectorComparison.length > 0 ? (
                                                <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                                                    {sectorComparison.map((sector, index) => (
                                                        <MotionCard
                                                            key={sector.sector || index}
                                                            initial={{ opacity: 0, scale: 0.95 }}
                                                            animate={{ opacity: 1, scale: 1 }}
                                                            transition={{ duration: 0.3, delay: index * 0.1 }}
                                                            bg="rgba(255, 255, 255, 0.05)"
                                                            borderWidth="1px"
                                                            borderColor="rgba(255, 255, 255, 0.1)"
                                                            borderRadius="lg"
                                                            p={4}
                                                            _hover={{
                                                                bg: "rgba(255, 255, 255, 0.08)",
                                                                borderColor: accentColor,
                                                                transform: "translateY(-2px)"
                                                            }}
                                                        >
                                                            <VStack align="start" spacing={3}>
                                                                <Text fontSize="lg" fontWeight="bold" color="#ffffff">
                                                                    <SafeDataRenderer data={sector.sector} fallback="Unknown Sector" />
                                                                </Text>
                                                                <VStack align="start" spacing={1} w="full">
                                                                    <HStack justify="space-between" w="full">
                                                                        <Text fontSize="sm" color="gray.400">Companies:</Text>
                                                                        <Text fontSize="sm" color={accentColor} fontWeight="bold">
                                                                            <SafeDataRenderer data={sector.companies_count} fallback={0} />
                                                                        </Text>
                                                                    </HStack>
                                                                    <HStack justify="space-between" w="full">
                                                                        <Text fontSize="sm" color="gray.400">Avg Volume:</Text>
                                                                        <Text fontSize="sm" color="#ffffff">
                                                                            <SafeDataRenderer data={sector.formatted_avg_volume} fallback="N/A" />
                                                                        </Text>
                                                                    </HStack>
                                                                    <HStack justify="space-between" w="full">
                                                                        <Text fontSize="sm" color="gray.400">Total Cap:</Text>
                                                                        <Text fontSize="sm" color="#ffffff">
                                                                            <SafeDataRenderer data={sector.formatted_total_cap} fallback="N/A" />
                                                                        </Text>
                                                                    </HStack>
                                                                </VStack>
                                                            </VStack>
                                                        </MotionCard>
                                                    ))}
                                                </SimpleGrid>
                                            ) : (
                                                <Text color="gray.500" textAlign="center" py={8}>
                                                    Loading sector comparison data...
                                                </Text>
                                            )}
                                        </CardBody>
                                    </MotionCard>

                                    {/* Sector Performance - Curve Tabs Format */}
                                    <MotionCard
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.5, delay: 0.2 }}
                                        whileHover={{ 
                                            boxShadow: "0 20px 40px rgba(255, 206, 48, 0.1)",
                                            transform: "translateY(-2px)"
                                        }}
                                        bg={cardBg}
                                        borderWidth="1px"
                                        borderColor={borderColor}
                                        borderRadius="xl"
                                        overflow="hidden"
                                        w="100%"
                                        position="relative"
                                        _before={{
                                            content: '""',
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            right: 0,
                                            height: '3px',
                                            background: 'linear-gradient(90deg, #10B981, #059669)'
                                        }}
                                    >
                                        <CardHeader>
                                            <HStack justify="space-between" align="center">
                                                <VStack align="start" spacing={1}>
                                                    <Heading size="md" color={accentColor}>Sector Performance</Heading>
                                                    <Text fontSize="sm" color="gray.500">
                                                        Index performance by sector
                                                    </Text>
                                                </VStack>
                                                <Icon as={FiTrendingUp} color="green.400" fontSize="24px" />
                                            </HStack>
                                        </CardHeader>
                                        <CardBody pt={0}>
                                            {sectorPerformance && sectorPerformance.length > 0 ? (
                                                <Tabs variant="enclosed" colorScheme="yellow">
                                                    <TabList mb={4}>
                                                        {sectorPerformance.slice(0, 4).map((sector, index) => (
                                                            <Tab key={sector.index_name || index} fontSize="sm">
                                                                {sector.index_name || `Index ${index + 1}`}
                                                            </Tab>
                                                        ))}
                                                    </TabList>
                                                    <TabPanels>
                                                        {sectorPerformance.slice(0, 4).map((sector, index) => (
                                                            <TabPanel key={sector.index_name || index} p={4}>
                                                                <VStack align="start" spacing={4}>
                                                                    <HStack justify="space-between" w="full">
                                                                        <Text fontSize="lg" fontWeight="bold" color="#ffffff">
                                                                            <SafeDataRenderer data={sector.index_name} fallback="Unknown Index" />
                                                                        </Text>
                                                                        <Text 
                                                                            fontSize="lg" 
                                                                            fontWeight="bold" 
                                                                            color={parseFloat(sector.change_percent || 0) >= 0 ? "green.400" : "red.400"}
                                                                        >
                                                                            <SafeDataRenderer data={sector.change_percent} fallback="0.00" />%
                                                                        </Text>
                                                                    </HStack>
                                                                    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4} w="full">
                                                                        <VStack align="start" spacing={1}>
                                                                            <Text fontSize="sm" color="gray.400">Latest Value</Text>
                                                                            <Text fontSize="md" color="#ffffff" fontWeight="bold">
                                                                                <SafeDataRenderer data={sector.latest_value} fallback="N/A" />
                                                                            </Text>
                                                                        </VStack>
                                                                        <VStack align="start" spacing={1}>
                                                                            <Text fontSize="sm" color="gray.400">Previous Value</Text>
                                                                            <Text fontSize="md" color="#ffffff">
                                                                                <SafeDataRenderer data={sector.previous_value} fallback="N/A" />
                                                                            </Text>
                                                                        </VStack>
                                                                        <VStack align="start" spacing={1}>
                                                                            <Text fontSize="sm" color="gray.400">Last Updated</Text>
                                                                            <Text fontSize="md" color="gray.300">
                                                                                <SafeDataRenderer data={sector.latest_date} fallback="N/A" />
                                                                            </Text>
                                                                        </VStack>
                                                                    </SimpleGrid>
                                                                </VStack>
                                                            </TabPanel>
                                                        ))}
                                                    </TabPanels>
                                                </Tabs>
                                            ) : (
                                                <Text color="gray.500" textAlign="center" py={8}>
                                                    Loading sector performance data...
                                                </Text>
                                            )}
                                        </CardBody>
                                    </MotionCard>
                                </VStack>
                            </VStack>
                        </TabPanel>

                        {/* Companies Data Tab */}
                        <TabPanel p={0}>
                            <VStack spacing={6}>
                                {/* Company Selection and Controls */}
                                <Card w="full" bg={cardBg} shadow="lg" borderRadius="xl">
                                    <CardBody>
                                        <VStack spacing={4}>
                                            <HStack w="full" justify="space-between" align="center">
                                                <Heading size="md" color={accentColor}>
                                                    <HStack spacing={2}>
                                                        <FiTrendingUp />
                                                        <Text>Company Analysis</Text>
                                                    </HStack>
                                                </Heading>
                                                <Button
                                                    leftIcon={refreshing ? <Spinner size="sm" /> : <FiRefreshCw />}
                                                    colorScheme="blue"
                                                    variant="outline"
                                                    size="sm"
                                                    isLoading={loadingCompanyData}
                                                    onClick={loadCompanyData}
                                                >
                                                    Refresh Data
                                                </Button>
                                            </HStack>
                                            
                                            <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4} w="full">
                                                {/* Company Selection with dark theme */}
                                                <FormControl>
                                                    <FormLabel fontSize="sm" fontWeight="600" color="#ffffff">Select Company</FormLabel>
                                                    <Select
                                                        value={selectedCompany}
                                                        onChange={(e) => setSelectedCompany(e.target.value)}
                                                        bg={inputBg}
                                                        borderColor={borderColor}
                                                        color="#ffffff"
                                                        _hover={{ borderColor: accentColor }}
                                                        _focus={{ borderColor: accentColor, boxShadow: `0 0 0 1px ${accentColor}` }}
                                                    >
                                                        <option value="" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Choose a company...</option>
                                                        {availableCompanies.map((company) => (
                                                            <option key={company.symbol} value={company.symbol} style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>
                                                                {company.nom_francais || company.nom_anglais || company.symbol} ({company.symbol})
                                                            </option>
                                                        ))}
                                                    </Select>
                                                </FormControl>
                                                
                                                {/* Period Selection with dark theme */}
                                                <FormControl>
                                                    <FormLabel fontSize="sm" fontWeight="600" color="#ffffff">Time Period</FormLabel>
                                                    <Select
                                                        value={companyPeriod}
                                                        onChange={(e) => setCompanyPeriod(e.target.value)}
                                                        bg={inputBg}
                                                        borderColor={borderColor}
                                                        color="#ffffff"
                                                        _hover={{ borderColor: accentColor }}
                                                        _focus={{ borderColor: accentColor, boxShadow: `0 0 0 1px ${accentColor}` }}
                                                    >
                                                        <option value="1M" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Last Month</option>
                                                        <option value="3M" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Last 3 Months</option>
                                                        <option value="6M" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Last 6 Months</option>
                                                        <option value="1Y" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Last Year</option>
                                                        <option value="2Y" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Last 2 Years</option>
                                                        <option value="3Y" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Last 3 Years</option>
                                                        <option value="ALL" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>All Data (Since 22/08/2022)</option>
                                                        <option value="CUSTOM" style={{ backgroundColor: '#1a1a1a', color: '#ffffff' }}>Custom Range</option>
                                                    </Select>
                                                </FormControl>
                                                
                                                {/* Custom Date Range with dark theme */}
                                                {companyPeriod === 'CUSTOM' && (
                                                    <>
                                                        <FormControl>
                                                            <FormLabel fontSize="sm" fontWeight="600" color="#ffffff">Start Date</FormLabel>
                                                            <Input
                                                                type="date"
                                                                value={companyCustomStartDate}
                                                                onChange={(e) => setCompanyCustomStartDate(e.target.value)}
                                                                min="2022-08-22"
                                                                max={companyCustomEndDate}
                                                                bg={inputBg}
                                                                borderColor={borderColor}
                                                                color="#ffffff"
                                                                _hover={{ borderColor: accentColor }}
                                                                _focus={{ borderColor: accentColor, boxShadow: `0 0 0 1px ${accentColor}` }}
                                                            />
                                                        </FormControl>
                                                        
                                                        <FormControl>
                                                            <FormLabel fontSize="sm" fontWeight="600" color="#ffffff">End Date</FormLabel>
                                                            <Input
                                                                type="date"
                                                                value={companyCustomEndDate}
                                                                onChange={(e) => setCompanyCustomEndDate(e.target.value)}
                                                                min={companyCustomStartDate}
                                                                max={new Date().toISOString().split('T')[0]}
                                                                bg={inputBg}
                                                                borderColor={borderColor}
                                                                color="#ffffff"
                                                                _hover={{ borderColor: accentColor }}
                                                                _focus={{ borderColor: accentColor, boxShadow: `0 0 0 1px ${accentColor}` }}
                                                            />
                                                        </FormControl>
                                                    </>
                                                )}
                                            </SimpleGrid>
                                            
                                            {companyPeriod === 'CUSTOM' && (
                                                    <Text fontSize="xs" color="gray.500" textAlign="center">
                                                        <HStack spacing={1} justify="center">
                                                            <FiCalendar size={12} />
                                                            <Text>Data is available from 22/08/2022 onwards</Text>
                                                        </HStack>
                                                    </Text>
                                            )}
                                        </VStack>
                                    </CardBody>
                                </Card>

                                {/* Company Chart */}
                                {selectedCompany && (
                                    <Card w="full" bg={cardBg} shadow="lg" borderRadius="xl">
                                        <CardBody>
                                            <VStack spacing={4}>
                                                <HStack w="full" justify="space-between">
                                                    <VStack align="start" spacing={1}>
                                                        <Heading size="md" color={accentColor}>
                                                            {companyDetails?.nom_francais || companyDetails?.nom_anglais || selectedCompany} Stock Price
                                                            {companyPeriod === 'ALL' && ' (Since 22/08/2022)'}
                                                            {companyPeriod === 'CUSTOM' && companyCustomStartDate && companyCustomEndDate && 
                                                                ` (${companyCustomStartDate} to ${companyCustomEndDate})`}
                                                            {!['ALL', 'CUSTOM'].includes(companyPeriod) && ` (${companyPeriod})`}
                                                        </Heading>
                                                        <Text fontSize="sm" color="gray.500">
                                                            {companyChartData.length} data points
                                                            {companyChartData.length > 0 && (
                                                                ` • ${companyChartData[companyChartData.length - 1]?.date} to ${companyChartData[0]?.date}`
                                                            )}
                                                        </Text>
                                                    </VStack>
                                                    
                                                    {companyDetails && (
                                                        <VStack align="end" spacing={1}>
                                                            <Text fontSize="lg" fontWeight="bold" color={accentColor}>
                                                                {companyDetails.current_price || companyDetails.last_price || 'N/A'} MAD
                                                            </Text>
                                                            <Badge 
                                                                colorScheme={companyDetails.daily_change_pct >= 0 ? "green" : "red"}
                                                                variant="solid"
                                                            >
                                                                {companyDetails.daily_change_pct >= 0 ? '+' : ''}{companyDetails.daily_change_pct || 0}%
                                                            </Badge>
                                                        </VStack>
                                                    )}
                                                </HStack>
                                                
                                                {loadingCompanyData ? (
                                                    <Center h="400px">
                                                        <VStack>
                                                            <Spinner size="lg" color={accentColor} />
                                                            <Text color="#888">Loading {selectedCompany} data...</Text>
                                                        </VStack>
                                                    </Center>
                                                ) : companyChartData.length > 0 ? (
                                                    <Box w="full" height="400px">
                                                        <ResponsiveContainer width="100%" height={350} minWidth={300} minHeight={300}>
                                                            <ComposedChart data={[...companyChartData].reverse()}>
                                                                <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                                                                <XAxis 
                                                                    dataKey="date" 
                                                                    fontSize={12}
                                                                    stroke="#718096"
                                                                    tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { 
                                                                        month: 'short', 
                                                                        day: 'numeric',
                                                                        ...(companyPeriod === '1M' ? {} : { year: '2-digit' })
                                                                    })}
                                                                />
                                                                <YAxis 
                                                                    yAxisId="price"
                                                                    fontSize={12}
                                                                    stroke="#718096"
                                                                    domain={['dataMin - 10', 'dataMax + 10']}
                                                                    tickFormatter={(value) => `${value.toFixed(1)}`}
                                                                />
                                                                <YAxis 
                                                                    yAxisId="volume" 
                                                                    orientation="right" 
                                                                    fontSize={12} 
                                                                    stroke="#718096"
                                                                    tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`}
                                                                />
                                                                <Tooltip 
                                                                    contentStyle={{
                                                                        backgroundColor: cardBg,
                                                                        border: `1px solid ${borderColor}`,
                                                                        borderRadius: '8px'
                                                                    }}
                                                                    formatter={(value, name) => [
                                                                        name === 'Volume' ? `${(value / 1000).toFixed(0)}K` : `${parseFloat(value).toFixed(2)} MAD`,
                                                                        name
                                                                    ]}
                                                                    labelFormatter={(label) => `Date: ${new Date(label).toLocaleDateString()}`}
                                                                />
                                                                <Legend />
                                                                <Area
                                                                    yAxisId="price"
                                                                    dataKey="price"
                                                                    stroke={accentColor}
                                                                    fill={`url(#colorCompanyPrice)`}
                                                                    strokeWidth={2}
                                                                    name="Stock Price"
                                                                />
                                                                <Bar 
                                                                    yAxisId="volume"
                                                                    dataKey="volume" 
                                                                    fill="#60a5fa" 
                                                                    opacity={0.6}
                                                                    name="Volume"
                                                                />
                                                                <defs>
                                                                    <linearGradient id="colorCompanyPrice" x1="0" y1="0" x2="0" y2="1">
                                                                        <stop offset="5%" stopColor={accentColor} stopOpacity={0.3}/>
                                                                        <stop offset="95%" stopColor={accentColor} stopOpacity={0}/>
                                                                    </linearGradient>
                                                                </defs>
                                                            </ComposedChart>
                                                        </ResponsiveContainer>
                                                    </Box>
                                                ) : selectedCompany ? (
                                                    <Center h="400px">
                                                        <VStack>
                                                            <Text color="#888" fontSize="lg">No chart data available</Text>
                                                            <Text color="gray.400" fontSize="sm">
                                                                {companyPeriod === 'ALL' && `No data found for ${selectedCompany} since 22/08/2022`}
                                                                {companyPeriod === 'CUSTOM' && companyCustomStartDate && companyCustomEndDate && 
                                                                    `No data found for ${selectedCompany} from ${companyCustomStartDate} to ${companyCustomEndDate}`}
                                                                {!['ALL', 'CUSTOM'].includes(companyPeriod) && 
                                                                    `No data available for ${selectedCompany} in ${companyPeriod} period`}
                                                            </Text>
                                                        </VStack>
                                                    </Center>
                                                ) : (
                                                    <Center h="400px">
                                                        <VStack>
                                                            <Text color="#888" fontSize="lg">Select a company to view chart</Text>
                                                            <Text color="gray.400" fontSize="sm">Choose a company from the dropdown above</Text>
                                                        </VStack>
                                                    </Center>
                                                )}
                                            </VStack>
                                        </CardBody>
                                    </Card>
                                )}

                                {/* Company Price Data Table */}
                                {selectedCompany && companyPricesData.length > 0 && (
                                    <Card w="full" bg={cardBg} shadow="lg" borderRadius="xl">
                                        <CardBody>
                                            <ProfessionalTable
                                                title={
                                                    <HStack spacing={2}>
                                                        <FiBarChart2 />
                                                        <Text>{companyDetails?.nom_francais || companyDetails?.nom_anglais || selectedCompany} Price History ({companyPeriod})</Text>
                                                    </HStack>
                                                }
                                                data={companyPricesData.slice().reverse().slice(0, 100)} // Show latest 100 records in reverse chronological order
                                                columns={[
                                                    { 
                                                        key: 'date', 
                                                        header: 'Date',
                                                        render: (value) => new Date(value).toLocaleDateString()
                                                    },
                                                    { 
                                                        key: 'open_price', 
                                                        header: 'Open',
                                                        render: (value, row) => {
                                                            const price = value || row.open || 0;
                                                            return `${parseFloat(price).toFixed(2)} MAD`;
                                                        }
                                                    },
                                                    { 
                                                        key: 'high_price', 
                                                        header: 'High',
                                                        render: (value, row) => {
                                                            const price = value || row.high || 0;
                                                            return `${parseFloat(price).toFixed(2)} MAD`;
                                                        }
                                                    },
                                                    { 
                                                        key: 'low_price', 
                                                        header: 'Low',
                                                        render: (value, row) => {
                                                            const price = value || row.low || 0;
                                                            return `${parseFloat(price).toFixed(2)} MAD`;
                                                        }
                                                    },
                                                    { 
                                                        key: 'close_price', 
                                                        header: 'Close',
                                                        render: (value, row) => {
                                                            const price = value || row.price || 0;
                                                            return `${parseFloat(price).toFixed(2)} MAD`;
                                                        }
                                                    },
                                                    { 
                                                        key: 'current_price', 
                                                        header: 'Current',
                                                        render: (value, row) => {
                                                            const price = value || row.current_price || row.close_price || 0;
                                                            return `${parseFloat(price).toFixed(2)} MAD`;
                                                        }
                                                    },
                                                    { 
                                                        key: 'volume', 
                                                        header: 'Volume',
                                                        render: (value) => parseInt(value || 0).toLocaleString()
                                                    },
                                                    { 
                                                        key: 'shares_traded', 
                                                        header: 'Shares Traded',
                                                        render: (value) => parseInt(value || 0).toLocaleString()
                                                    },
                                                    { 
                                                        key: 'total_trades', 
                                                        header: 'Total Trades',
                                                        render: (value) => parseInt(value || 0).toLocaleString()
                                                    },
                                                    { 
                                                        key: 'market_cap', 
                                                        header: 'Market Cap',
                                                        render: (value) => {
                                                            const cap = parseFloat(value || 0);
                                                            if (cap >= 1e9) return `${(cap / 1e9).toFixed(2)}B MAD`;
                                                            if (cap >= 1e6) return `${(cap / 1e6).toFixed(2)}M MAD`;
                                                            return `${cap.toFixed(2)} MAD`;
                                                        }
                                                    }
                                                ]}
                                            />
                                        </CardBody>
                                    </Card>
                                )}
                            </VStack>
                        </TabPanel>

                        {/* Performance Tab */}
                        <TabPanel p={0}>
                            <VStack spacing={6}>
                                {/* Market Statistics Section */}
                                <MotionCard
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.5 }}
                                    whileHover={{ 
                                        boxShadow: "0 20px 40px rgba(255, 206, 48, 0.1)",
                                        transform: "translateY(-2px)"
                                    }}
                                    bg={cardBg}
                                    borderWidth="1px"
                                    borderColor={borderColor}
                                    borderRadius="xl"
                                    overflow="hidden"
                                    w="100%"
                                    position="relative"
                                    _before={{
                                        content: '""',
                                        position: 'absolute',
                                        top: 0,
                                        left: 0,
                                        right: 0,
                                        height: '3px',
                                        background: 'linear-gradient(90deg, #FFD700, #FF8C00)'
                                    }}
                                >
                                    <CardHeader>
                                        <HStack justify="space-between" align="center">
                                            <VStack align="start" spacing={1}>
                                                <Heading size="md" color={accentColor}>Market Statistics</Heading>
                                                <Text fontSize="sm" color="gray.500">
                                                    Comprehensive market overview and analytics
                                                </Text>
                                            </VStack>
                                            <Icon as={FiPieChart} color={accentColor} fontSize="24px" />
                                        </HStack>
                                    </CardHeader>
                                    <CardBody pt={0}>
                                        {marketStatistics ? (
                                            <VStack spacing={4}>
                                                {/* Debug info - remove in production */}
                                                {process.env.NODE_ENV === 'development' && (
                                                    <Text fontSize="xs" color="gray.600">
                                                        Market Stats Keys: {marketStatistics ? Object.keys(marketStatistics).join(', ') : 'None'}
                                                    </Text>
                                                )}
                                                
                                                <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
                                                {/* Total Companies */}
                                                <VStack align="start" spacing={3} p={4} bg="rgba(255, 255, 255, 0.05)" borderRadius="lg">
                                                    <HStack>
                                                        <Icon as={FiUsers} color="blue.400" />
                                                        <Text fontSize="sm" color="gray.400">Total Companies</Text>
                                                    </HStack>
                                                    <Text fontSize="2xl" fontWeight="bold" color="#ffffff">
                                                        {marketStatistics.market_overview?.total_companies || 0}
                                                    </Text>
                                                </VStack>

                                                {/* Total Market Cap */}
                                                <VStack align="start" spacing={3} p={4} bg="rgba(255, 255, 255, 0.05)" borderRadius="lg">
                                                    <HStack>
                                                        <Icon as={FiTrendingUp} color="green.400" />
                                                        <Text fontSize="sm" color="gray.400">Market Capitalization</Text>
                                                    </HStack>
                                                    <Text fontSize="2xl" fontWeight="bold" color="#ffffff">
                                                        {marketStatistics.market_overview?.total_market_cap_formatted || 'N/A'}
                                                    </Text>
                                                </VStack>

                                                {/* Average Volume */}
                                                <VStack align="start" spacing={3} p={4} bg="rgba(255, 255, 255, 0.05)" borderRadius="lg">
                                                    <HStack>
                                                        <Icon as={FiActivity} color="purple.400" />
                                                        <Text fontSize="sm" color="gray.400">Avg Daily Volume</Text>
                                                    </HStack>
                                                    <Text fontSize="2xl" fontWeight="bold" color="#ffffff">
                                                        {marketStatistics.market_overview?.total_volume_formatted || 'N/A'}
                                                    </Text>
                                                </VStack>

                                                {/* Active Indices */}
                                                <VStack align="start" spacing={3} p={4} bg="rgba(255, 255, 255, 0.05)" borderRadius="lg">
                                                    <HStack>
                                                        <Icon as={FiBarChart} color="orange.400" />
                                                        <Text fontSize="sm" color="gray.400">Active Indices</Text>
                                                    </HStack>
                                                    <Text fontSize="2xl" fontWeight="bold" color="#ffffff">
                                                        {marketStatistics.indices_overview?.total_indices || 0}
                                                    </Text>
                                                </VStack>
                                            </SimpleGrid>
                                            </VStack>
                                        ) : (
                                            <Text color="gray.500" textAlign="center" py={8}>
                                                Loading market statistics...
                                            </Text>
                                        )}
                                    </CardBody>
                                </MotionCard>

                                {/* Market Indices Performance Table */}
                                <Card w="full" bg={cardBg} shadow="lg" borderRadius="xl">
                                    <CardBody>
                                        <ProfessionalTable
                                            title={
                                                <HStack spacing={2}>
                                                    <MdShowChart />
                                                    <Text>Market Indices Performance</Text>
                                                </HStack>
                                            }
                                            data={indicesData}
                                            columns={[
                                                { 
                                                    key: 'name', 
                                                    header: 'Index Name',
                                                    render: (value, row) => (
                                                        <HStack>
                                                            <Avatar size="sm" name={value} bg={accentColor} />
                                                            <VStack align="start" spacing={0}>
                                                                <Text fontWeight="bold">{value || row.symbol}</Text>
                                                                <Text fontSize="xs" color="gray.500">{row.id}</Text>
                                                            </VStack>
                                                        </HStack>
                                                    )
                                                },
                                                { 
                                                    key: 'current_value', 
                                                    header: 'Current Value',
                                                    render: (value) => (
                                                        <Text fontWeight="bold" color={accentColor}>
                                                            {parseFloat(value || 0).toLocaleString()}
                                                        </Text>
                                                    )
                                                },
                                                { 
                                                    key: 'daily_change_pct', 
                                                    header: 'Change %',
                                                    render: (value, row) => {
                                                        const change = parseFloat(value || row.change_percent || 0);
                                                        return (
                                                            <HStack>
                                                                <Text fontSize="sm" color={change >= 0 ? successColor : errorColor}>
                                                                    {change >= 0 ? '▲' : '▼'}
                                                                </Text>
                                                                <Text 
                                                                    color={change >= 0 ? successColor : errorColor}
                                                                    fontWeight="bold"
                                                                >
                                                                    {Math.abs(change).toFixed(2)}%
                                                                </Text>
                                                            </HStack>
                                                        );
                                                    }
                                                },
                                                { 
                                                    key: 'daily_change', 
                                                    header: 'Daily Change',
                                                    render: (value) => {
                                                        const change = parseFloat(value || 0);
                                                        return (
                                                            <Text color={change >= 0 ? successColor : errorColor}>
                                                                {change > 0 ? '+' : ''}{change.toFixed(2)}
                                                            </Text>
                                                        );
                                                    }
                                                },
                                                { 
                                                    key: 'last_update', 
                                                    header: 'Last Update',
                                                    render: (value) => (
                                                        <Text fontSize="sm" color="gray.500">
                                                            {value === 'N/A' ? value : new Date(value).toLocaleDateString()}
                                                        </Text>
                                                    )
                                                }
                                            ]}
                                            maxHeight="400px"
                                        />
                                    </CardBody>
                                </Card>

                                {/* Companies Performance Table */}
                                <Card w="full" bg={cardBg} shadow="lg" borderRadius="xl">
                                    <CardBody>
                                        <ProfessionalTable
                                            title={
                                                <HStack spacing={2}>
                                                    <MdCorporateFare />
                                                    <Text>Complete Companies Performance Analysis</Text>
                                                </HStack>
                                            }
                                            data={companiesData}
                                            columns={[
                                                { 
                                                    key: 'name', 
                                                    header: 'Company',
                                                    render: (value, row) => (
                                                        <HStack>
                                                            <Avatar size="sm" name={value} bg={accentColor} />
                                                            <VStack align="start" spacing={0}>
                                                                <Text fontWeight="bold">{value || row.symbol}</Text>
                                                                <Text fontSize="xs" color="gray.500">{row.symbol}</Text>
                                                            </VStack>
                                                        </HStack>
                                                    )
                                                },
                                                { 
                                                    key: 'current_price', 
                                                    header: 'Current Price',
                                                    render: (value) => (
                                                        <Text fontWeight="bold" color={accentColor}>
                                                            {parseFloat(value || 0).toFixed(2)} MAD
                                                        </Text>
                                                    )
                                                },
                                                { 
                                                    key: 'variation', 
                                                    header: 'Change %',
                                                    render: (value, row) => {
                                                        const change = parseFloat(value || row.change_percent || 0);
                                                        return (
                                                            <HStack>
                                                                <Text fontSize="sm" color={change >= 0 ? successColor : errorColor}>
                                                                    {change >= 0 ? '▲' : '▼'}
                                                                </Text>
                                                                <Text 
                                                                    color={change >= 0 ? successColor : errorColor}
                                                                    fontWeight="bold"
                                                                >
                                                                    {Math.abs(change).toFixed(2)}%
                                                                </Text>
                                                            </HStack>
                                                        );
                                                    }
                                                },
                                                { 
                                                    key: 'volume', 
                                                    header: 'Volume',
                                                    render: (value) => (
                                                        <Text>{parseFloat(value || 0).toLocaleString()}</Text>
                                                    )
                                                },
                                                { 
                                                    key: 'market_cap', 
                                                    header: 'Market Cap',
                                                    render: (value) => (
                                                        <Text>
                                                            {value ? `${(parseFloat(value) / 1000000000).toFixed(1)}B` : 'N/A'}
                                                        </Text>
                                                    )
                                                }
                                            ]}
                                            maxHeight="600px"
                                        />
                                    </CardBody>
                                </Card>
                            </VStack>
                        </TabPanel>
                    </TabPanels>
                    </Tabs>
                </MotionBox>

                {/* Live Status Footer */}
                {realTimeMode && (
                    <MotionBox
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        position="fixed"
                        bottom={4}
                        right={4}
                        zIndex={1000}
                    >
                        <Card bg="green.500" color="white" size="sm">
                            <CardBody p={2}>
                                <HStack spacing={2}>
                                    <Box 
                                        w={2} 
                                        h={2} 
                                        borderRadius="full" 
                                        bg="white"
                                        animation="pulse 2s infinite"
                                    />
                                    <Text fontSize="xs" fontWeight="bold">LIVE DATA</Text>
                                </HStack>
                            </CardBody>
                        </Card>
                    </MotionBox>
                )}
            </Box>
        </MotionBox>
    );
};

export default Dashboard2Supreme;
