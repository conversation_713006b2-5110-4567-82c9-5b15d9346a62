import React from 'react';
import {Bar} from "react-chartjs-2";
import {Utils} from "../../../../../../../utils/Utils";

const RevenueChart = () => {
    const performanceData = {
        labels: Utils.getYearsArray(2018, 2024),
        datasets: [{
            label: 'Revenue',
            data: [180, 175, 95, 75, 400, 305, 10],
            backgroundColor: 'rgb(255,118,150)',
        }, {
            label: 'Net Income',
            data: [190, 340, 65, 205, 100, 150, 65],
            backgroundColor: 'rgb(90,180,255)',
        }]
    };
    const config = {
        type: 'bar',
        data: performanceData
    }
    const options = {
        plugins: {
            legend: {
                labels: {
                    color: 'white'
                }
            },
        },
        scales: {
            x: {
                grid: {
                    color: 'white'
                },
                ticks: {
                    color: 'white'
                }
            },
            y: {
                grid: {
                    color: 'white'
                },
                ticks: {
                    stepSize: 100,
                    color: 'white'
                }
            }
        }
    }
    return (
        <div>
            <Bar height='100px' data={config.data} options={options}/>
        </div>
    );
};

export default RevenueChart;