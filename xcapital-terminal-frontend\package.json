{"name": "xcapital-terminal", "version": "0.1.0", "private": true, "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@redux-devtools/extension": "^3.3.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "apexcharts": ">=4.0.0", "axios": "^1.7.8", "bootstrap": "^5.3.3", "chart.js": "^4.4.4", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-adapter-moment": "^1.0.1", "chartjs-chart-treemap": "^3.0.0", "chartjs-plugin-datalabels": "^2.2.0", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "flag-icons": "^7.2.3", "framer-motion": "^11.18.2", "igniteui-react-core": "^18.9.0", "igniteui-react-gauges": "^18.9.0", "jwt-decode": "^4.0.0", "moment": "^2.30.1", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-redux": "^9.1.2", "react-router-dom": "^6.30.0", "react-scripts": "^5.0.1", "recharts": "^3.1.2", "redux-thunk": "^3.1.0", "swiper": "^11.2.6", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "build:large": "cross-env NODE_OPTIONS='--max-old-space-size=4096' react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "react-scripts start"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11"}}