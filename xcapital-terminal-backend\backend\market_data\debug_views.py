"""
Vue de test pour diagnostiquer le problème du décorateur @api_view
"""
from rest_framework.decorators import api_view
from rest_framework.response import Response
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse

@csrf_exempt
@api_view(['GET', 'POST'])
def debug_indices_form(request):
    """
    Version simplifiée pour diagnostiquer le problème
    """
    if request.method == 'GET':
        return Response({
            'message': 'GET fonctionne!',
            'method': 'GET'
        })
    elif request.method == 'POST':
        return Response({
            'message': 'POST fonctionne!',
            'method': 'POST',
            'data_received': request.data
        })
    else:
        return Response({
            'error': f'Méthode {request.method} non supportée'
        }, status=405)

# Version alternative sans @api_view pour comparaison
@csrf_exempt
def debug_indices_form_alternative(request):
    """
    Version alternative sans @api_view
    """
    if request.method == 'GET':
        return JsonResponse({
            'message': 'GET fonctionne (sans @api_view)!',
            'method': 'GET'
        })
    elif request.method == 'POST':
        import json
        try:
            data = json.loads(request.body) if request.body else {}
        except:
            data = {}
        return JsonResponse({
            'message': 'POST fonctionne (sans @api_view)!',
            'method': 'POST',
            'data_received': data
        })
    else:
        return JsonResponse({
            'error': f'Méthode {request.method} non supportée'
        }, status=405)
