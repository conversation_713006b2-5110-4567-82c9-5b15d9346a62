"""
Serializers pour les modèles PostgreSQL XCapital
"""
from rest_framework import serializers
from .postgres_models import XCapitalCompany, XCapitalCompanyBond, XCapitalIndex, XCapitalIndexValue
from datetime import datetime, date
from decimal import Decimal


class XCapitalCompanySerializer(serializers.ModelSerializer):
    """Serializer pour les entreprises XCapital"""
    
    display_name = serializers.ReadOnlyField()
    latest_price = serializers.SerializerMethodField()
    price_change = serializers.SerializerMethodField()
    total_bonds = serializers.SerializerMethodField()
    
    class Meta:
        model = XCapitalCompany
        fields = [
            'id', 'symbol', 'company_id', 'nom_francais', 'nom_arabe', 
            'nom_anglais', 'display_name', 'latest_price', 'price_change',
            'total_bonds', 'created_at', 'updated_at'
        ]
    
    def get_latest_price(self, obj):
        """Récupérer le dernier prix"""
        try:
            latest_bond = obj.bonds.order_by('-date_trade').first()
            if latest_bond:
                return {
                    'date': latest_bond.date_trade,
                    'close_price': float(latest_bond.close_price) if latest_bond.close_price else None,
                    'current_price': float(latest_bond.current_price) if latest_bond.current_price else None,
                    'volume': latest_bond.volume
                }
            return None
        except:
            return None
    
    def get_price_change(self, obj):
        """Récupérer la variation de prix"""
        try:
            latest_bonds = obj.bonds.order_by('-date_trade')[:2]
            if len(latest_bonds) >= 2:
                current = latest_bonds[0]
                previous = latest_bonds[1]
                
                if current.close_price and previous.close_price:
                    change = float(current.close_price - previous.close_price)
                    change_pct = (change / float(previous.close_price)) * 100
                    return {
                        'absolute': round(change, 4),
                        'percentage': round(change_pct, 2)
                    }
            return None
        except:
            return None
    
    def get_total_bonds(self, obj):
        """Nombre total de records de prix"""
        return obj.bonds.count()


class XCapitalCompanyBondSerializer(serializers.ModelSerializer):
    """Serializer pour les données de prix (bonds)"""
    
    company_symbol = serializers.CharField(source='company.symbol', read_only=True)
    company_name = serializers.CharField(source='company.display_name', read_only=True)
    daily_change = serializers.ReadOnlyField()
    daily_change_pct = serializers.ReadOnlyField()
    
    class Meta:
        model = XCapitalCompanyBond
        fields = [
            'id', 'company_symbol', 'company_name', 'date_trade',
            'open_price', 'high_price', 'low_price', 'close_price',
            'current_price', 'volume', 'shares_traded', 'total_trades',
            'market_cap', 'adjusted_close', 'consolidated_ratio',
            'daily_change', 'daily_change_pct', 'created_at', 'updated_at'
        ]


class XCapitalPriceDataRequestSerializer(serializers.Serializer):
    """Serializer pour valider les requêtes de données de prix via POST"""
    
    symbol = serializers.CharField(
        max_length=50,
        help_text="Symbole de l'instrument (ex: ATTIJARIWAFA_BANK)"
    )
    
    # Période prédéfinie ou dates personnalisées
    period = serializers.ChoiceField(
        choices=[
            ('1M', '1 Mois'),
            ('3M', '3 Mois'), 
            ('6M', '6 Mois'),
            ('1Y', '1 Année'),
            ('3Y', '3 Années'),
            ('ALL', 'Toutes les données'),
            ('CUSTOM', 'Période personnalisée')
        ],
        required=False,
        help_text="Période prédéfinie"
    )
    
    start_date = serializers.DateField(
        required=False,
        help_text="Date de début (YYYY-MM-DD). Ne peut pas être antérieure au 15-08-2022"
    )
    
    end_date = serializers.DateField(
        required=False,
        help_text="Date de fin (YYYY-MM-DD). Par défaut aujourd'hui"
    )
    
    def validate(self, data):
        """Validation globale des données"""
        period = data.get('period')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        # Si période personnalisée, les dates sont obligatoires
        if period == 'CUSTOM':
            if not start_date:
                raise serializers.ValidationError(
                    "start_date est obligatoire pour une période personnalisée"
                )
        
        # Validation de la date minimale
        min_date = date(2022, 8, 15)
        if start_date and start_date < min_date:
            raise serializers.ValidationError(
                f"La date de début ne peut pas être antérieure au {min_date.strftime('%d-%m-%Y')}"
            )
        
        # Validation de la date maximale (aujourd'hui)
        today = date.today()
        if end_date and end_date > today:
            raise serializers.ValidationError(
                f"La date de fin ne peut pas être supérieure à aujourd'hui ({today.strftime('%d-%m-%Y')})"
            )
        
        # Validation de l'ordre des dates
        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError(
                "La date de début doit être antérieure à la date de fin"
            )
        
        # Si pas de période spécifiée, utiliser ALL par défaut
        if not period:
            data['period'] = 'ALL'
        
        return data
    
    def validate_symbol(self, value):
        """Validation du symbole d'instrument"""
        try:
            company = XCapitalCompany.objects.get(symbol=value)
            return value
        except XCapitalCompany.DoesNotExist:
            raise serializers.ValidationError(
                f"Instrument '{value}' non trouvé dans la base de données"
            )


class XCapitalPriceVariationSerializer(serializers.Serializer):
    """Serializer pour les variations de prix avec calculs automatiques"""
    
    date = serializers.DateField()
    open = serializers.DecimalField(max_digits=15, decimal_places=4, source='open_price')
    high = serializers.DecimalField(max_digits=15, decimal_places=4, source='high_price')
    low = serializers.DecimalField(max_digits=15, decimal_places=4, source='low_price')
    close = serializers.DecimalField(max_digits=15, decimal_places=4, source='close_price')
    volume = serializers.IntegerField()
    daily_change = serializers.DecimalField(max_digits=15, decimal_places=4)
    daily_change_pct = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_change = serializers.DecimalField(max_digits=15, decimal_places=4)
    total_change_pct = serializers.DecimalField(max_digits=10, decimal_places=2)


class XCapitalMarketSummarySerializer(serializers.Serializer):
    """Serializer pour le résumé du marché"""
    
    symbol = serializers.CharField()
    label = serializers.CharField()
    period = serializers.CharField()
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    first_price = serializers.DecimalField(max_digits=15, decimal_places=4)
    last_price = serializers.DecimalField(max_digits=15, decimal_places=4)
    total_change = serializers.DecimalField(max_digits=15, decimal_places=4)
    total_change_pct = serializers.DecimalField(max_digits=10, decimal_places=2)
    highest_price = serializers.DecimalField(max_digits=15, decimal_places=4)
    lowest_price = serializers.DecimalField(max_digits=15, decimal_places=4)
    average_price = serializers.DecimalField(max_digits=15, decimal_places=4)
    total_volume = serializers.IntegerField()
    data_points = serializers.IntegerField()
    data_source = serializers.CharField()


class XCapitalIndexSerializer(serializers.ModelSerializer):
    """Serializer pour les indices XCapital"""
    
    display_name = serializers.ReadOnlyField()
    latest_value = serializers.SerializerMethodField()
    value_change = serializers.SerializerMethodField()
    total_values = serializers.SerializerMethodField()
    
    class Meta:
        model = XCapitalIndex
        fields = [
            'index_id', 'index_name', 'display_name', 'latest_value', 
            'value_change', 'total_values', 'created_at', 'updated_at'
        ]
    
    def get_latest_value(self, obj):
        """Récupérer la dernière valeur"""
        try:
            latest_value = obj.values.order_by('-date_value').first()
            if latest_value:
                return {
                    'date': latest_value.date_value,
                    'value': float(latest_value.value) if latest_value.value else None,
                    'previous_value': float(latest_value.previous_value) if latest_value.previous_value else None,
                    'daily_change': float(latest_value.daily_change) if latest_value.daily_change else None,
                    'daily_change_pct': float(latest_value.daily_change_pct) if latest_value.daily_change_pct else None
                }
            return None
        except:
            return None
    
    def get_value_change(self, obj):
        """Récupérer la variation de valeur"""
        try:
            latest_values = obj.values.order_by('-date_value')[:2]
            if len(latest_values) >= 2:
                current = latest_values[0]
                previous = latest_values[1]
                
                if current.value and previous.value:
                    change = float(current.value - previous.value)
                    change_pct = (change / float(previous.value)) * 100
                    return {
                        'absolute': round(change, 4),
                        'percentage': round(change_pct, 2)
                    }
            return None
        except:
            return None
    
    def get_total_values(self, obj):
        """Nombre total de records de valeurs"""
        return obj.values.count()


class XCapitalIndexValueSerializer(serializers.ModelSerializer):
    """Serializer pour les données de valeurs d'indices"""
    
    index_id = serializers.CharField(source='index_table.index_id', read_only=True)
    index_name = serializers.CharField(source='index_table.index_name', read_only=True)
    calculated_daily_change = serializers.ReadOnlyField()
    calculated_daily_change_pct = serializers.ReadOnlyField()
    
    class Meta:
        model = XCapitalIndexValue
        fields = [
            'id', 'index_id', 'index_name', 'date_value',
            'value', 'previous_value', 'daily_change', 'daily_change_pct',
            'calculated_daily_change', 'calculated_daily_change_pct',
            'created_at', 'updated_at'
        ]


class XCapitalIndexDataRequestSerializer(serializers.Serializer):
    """Serializer pour valider les requêtes de données d'indices via POST"""
    
    PERIOD_CHOICES = [
        ('1M', '1 Mois'),
        ('3M', '3 Mois'),
        ('6M', '6 Mois'),
        ('1Y', '1 An'),
        ('3Y', '3 Ans'),
        ('ALL', 'Toutes les données'),
        ('CUSTOM', 'Période personnalisée'),
        ('SINCE_2022', 'Depuis 2022')
    ]
    
    # Accepter les deux noms de champs pour la compatibilité
    index_id = serializers.CharField(
        max_length=100, 
        required=False,
        help_text="ID de l'indice"
    )
    indices = serializers.CharField(
        max_length=100, 
        required=False,
        help_text="ID de l'indice (alias pour index_id)"
    )
    period = serializers.ChoiceField(
        choices=PERIOD_CHOICES,
        default='ALL',
        help_text="Période des données à récupérer"
    )
    start_date = serializers.DateField(
        required=False,
        help_text="Date de début (requis si period=CUSTOM)"
    )
    end_date = serializers.DateField(
        required=False,
        help_text="Date de fin (optionnel, par défaut = aujourd'hui)"
    )
    
    def validate(self, data):
        """Validation globale des données"""
        index_id = data.get('index_id')
        indices = data.get('indices')
        period = data.get('period')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        # Validation de l'index_id : accepter soit index_id soit indices
        if not index_id and not indices:
            raise serializers.ValidationError({
                'index_id': 'Veuillez fournir soit index_id soit indices'
            })
        
        # Utiliser indices si index_id n'est pas fourni
        if not index_id and indices:
            data['index_id'] = indices
        elif index_id and not indices:
            data['index_id'] = index_id
        elif index_id and indices:
            # Si les deux sont fournis, utiliser index_id et vérifier qu'ils sont identiques
            if index_id != indices:
                raise serializers.ValidationError({
                    'indices': 'Si les deux sont fournis, index_id et indices doivent être identiques'
                })
            data['index_id'] = index_id
        
        # Valider que l'indice existe
        final_index_id = data.get('index_id')
        if final_index_id:
            try:
                XCapitalIndex.objects.get(index_id=final_index_id)
            except XCapitalIndex.DoesNotExist:
                raise serializers.ValidationError({
                    'index_id': f"Indice '{final_index_id}' non trouvé dans la base de données"
                })
        
        # Validation pour période personnalisée
        if period == 'CUSTOM':
            if not start_date:
                raise serializers.ValidationError({
                    'start_date': 'Date de début requise pour une période personnalisée'
                })
        
        # Validation des dates
        if start_date and end_date:
            if start_date > end_date:
                raise serializers.ValidationError({
                    'start_date': 'La date de début doit être antérieure à la date de fin'
                })
        
        # Si pas de période spécifiée, utiliser ALL par défaut
        if not period:
            data['period'] = 'ALL'
        
        return data
    

class XCapitalIndexVariationSerializer(serializers.Serializer):
    """Serializer pour les variations d'indices avec calculs automatiques"""
    
    date = serializers.DateField(source='date_value')
    value = serializers.DecimalField(max_digits=15, decimal_places=4)
    previous_value = serializers.DecimalField(max_digits=15, decimal_places=4)
    daily_change = serializers.DecimalField(max_digits=15, decimal_places=4)
    daily_change_pct = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_change = serializers.DecimalField(max_digits=15, decimal_places=4)
    total_change_pct = serializers.DecimalField(max_digits=10, decimal_places=2)


class XCapitalIndexSummarySerializer(serializers.Serializer):
    """Serializer pour le résumé des indices"""
    
    index_id = serializers.CharField()
    index_name = serializers.CharField()
    period = serializers.CharField()
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    first_value = serializers.DecimalField(max_digits=15, decimal_places=4)
    last_value = serializers.DecimalField(max_digits=15, decimal_places=4)
    total_change = serializers.DecimalField(max_digits=15, decimal_places=4)
    total_change_pct = serializers.DecimalField(max_digits=10, decimal_places=2)
    highest_value = serializers.DecimalField(max_digits=15, decimal_places=4)
    lowest_value = serializers.DecimalField(max_digits=15, decimal_places=4)
    average_value = serializers.DecimalField(max_digits=15, decimal_places=4)
    data_points = serializers.IntegerField()
    data_source = serializers.CharField()
