"""
Vues optimisées pour les APIs XCapital avec cache et optimisations de performance
"""
from rest_framework import generics, status, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Sum, Avg, Max, Min, Prefetch
from django.db import models, connection
from django.core.cache import cache, caches
from django.views.decorators.cache import cache_page
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.utils import timezone
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta, date
from decimal import Decimal
import logging
import hashlib

from .postgres_models import XCapitalCompany, XCapitalCompanyBond
from .postgres_serializers import XCapitalCompanyBondSerializer

logger = logging.getLogger(__name__)

class OptimizedPagination(PageNumberPagination):
    """Pagination optimisée avec tailles variables"""
    page_size = 100
    page_size_query_param = 'page_size'
    max_page_size = 1000
    
    def get_paginated_response(self, data):
        return Response({
            'links': {
                'next': self.get_next_link(),
                'previous': self.get_previous_link()
            },
            'count': self.page.paginator.count,
            'page_size': self.page_size,
            'total_pages': self.page.paginator.num_pages,
            'current_page': self.page.number,
            'results': data
        })


def generate_cache_key(prefix, **kwargs):
    """Génère une clé de cache unique basée sur les paramètres"""
    key_data = f"{prefix}:" + ":".join([f"{k}={v}" for k, v in sorted(kwargs.items())])
    return hashlib.md5(key_data.encode()).hexdigest()


@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
@csrf_exempt
def optimized_company_price_data(request):
    """
    API optimisée pour récupérer les données de prix avec cache et pagination
    """
    client_ip = request.META.get('HTTP_X_FORWARDED_FOR', request.META.get('REMOTE_ADDR', 'Unknown'))
    logger.info(f"Optimized API Request from IP: {client_ip} - Method: {request.method}")
    
    if request.method == 'GET':
        return Response({
            'success': True,
            'api_info': {
                'name': 'API XCapital Optimisée',
                'version': '2.0',
                'description': 'API haute performance avec cache et pagination',
                'features': [
                    'Cache intelligent multi-niveau',
                    'Pagination optimisée',
                    'Requêtes SQL optimisées',
                    'Limitation automatique des données',
                    'Indexation avancée'
                ]
            },
            'performance_improvements': {
                'cache_layers': ['Memory cache', 'Query cache', 'Result cache'],
                'max_records_per_request': 10000,
                'typical_response_time': '< 500ms',
                'cache_duration': '5-60 minutes selon le type de données'
            }
        })
    
    elif request.method == 'POST':
        try:
            # Validation des données
            company_symbol = request.data.get('company_symbol', '').strip().upper()
            start_date = request.data.get('start_date', '2022-08-22')
            end_date = request.data.get('end_date', timezone.now().date().strftime('%Y-%m-%d'))
            page_size = min(int(request.data.get('page_size', 1000)), 10000)
            use_cache = request.data.get('use_cache', True)
            
            if not company_symbol:
                return Response({
                    'error': 'Symbole d\'entreprise requis',
                    'required_fields': ['company_symbol']
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Génération de la clé de cache
            cache_key = generate_cache_key(
                'company_price_data_v2',
                symbol=company_symbol,
                start=start_date,
                end=end_date,
                page_size=page_size
            )
            
            # Vérifier le cache d'abord
            if use_cache:
                cached_result = cache.get(cache_key)
                if cached_result:
                    logger.info(f"Cache hit for {company_symbol}")
                    cached_result['cache_info'] = {
                        'cache_hit': True,
                        'cache_key': cache_key[:16] + '...',
                        'cached_at': cached_result.get('timestamp')
                    }
                    return Response(cached_result, status=status.HTTP_200_OK)
            
            # Validation des dates
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError as e:
                return Response({
                    'error': 'Format de date invalide',
                    'message': 'Utilisez le format YYYY-MM-DD',
                    'error_detail': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Récupération optimisée de l'entreprise avec cache
            company_cache_key = f"company:{company_symbol}"
            company = cache.get(company_cache_key)
            
            if not company:
                try:
                    company = XCapitalCompany.objects.get(symbol=company_symbol)
                    cache.set(company_cache_key, company, 3600)  # Cache 1 heure
                except XCapitalCompany.DoesNotExist:
                    return Response({
                        'error': f'Entreprise "{company_symbol}" non trouvée'
                    }, status=status.HTTP_404_NOT_FOUND)
            
            # Requête optimisée avec select_related et limitation
            queryset = XCapitalCompanyBond.objects.select_related('company').filter(
                company=company,
                date_trade__gte=start_date_obj,
                date_trade__lte=end_date_obj
            ).order_by('date_trade')
            
            # Limitation de sécurité
            total_count = queryset.count()
            if total_count > 50000:  # Limite de sécurité
                return Response({
                    'error': 'Trop de données demandées',
                    'message': f'La période demandée contient {total_count} enregistrements. Limite: 50,000',
                    'suggestion': 'Réduisez la période ou utilisez la pagination'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Application de la pagination
            price_data = queryset[:page_size]
            
            # Formatage optimisé des données
            formatted_data = []
            for bond in price_data:
                formatted_data.append({
                    'date': bond.date_trade.strftime('%Y-%m-%d'),
                    'open_price': float(bond.open_price) if bond.open_price else None,
                    'high_price': float(bond.high_price) if bond.high_price else None,
                    'low_price': float(bond.low_price) if bond.low_price else None,
                    'close_price': float(bond.close_price) if bond.close_price else None,
                    'volume': bond.volume if bond.volume else 0,
                    'market_cap': float(bond.market_cap) if bond.market_cap else None,
                })
            
            # Calcul des statistiques avec agrégations optimisées
            if price_data:
                stats = queryset.aggregate(
                    max_price=Max('high_price'),
                    min_price=Min('low_price'),
                    avg_volume=Avg('volume'),
                    total_volume=Sum('volume')
                )
                
                first_record = price_data[0] if price_data else None
                last_record = price_data[len(price_data)-1] if price_data else None
                
                first_price = first_record.close_price if first_record and first_record.close_price else Decimal('0')
                last_price = last_record.close_price if last_record and last_record.close_price else Decimal('0')
                
                period_change = last_price - first_price if first_price and last_price else Decimal('0')
                period_change_pct = (period_change / first_price * 100) if first_price else Decimal('0')
            else:
                stats = {'max_price': 0, 'min_price': 0, 'avg_volume': 0, 'total_volume': 0}
                first_price = last_price = period_change = period_change_pct = Decimal('0')
            
            # Préparation de la réponse
            response_data = {
                'success': True,
                'timestamp': timezone.now().isoformat(),
                'company_info': {
                    'symbol': company.symbol,
                    'nom_francais': company.nom_francais,
                    'company_id': company.company_id
                },
                'period': {
                    'start_date': start_date,
                    'end_date': end_date,
                    'total_available_records': total_count,
                    'returned_records': len(formatted_data),
                    'page_size': page_size
                },
                'statistics': {
                    'first_price': float(first_price),
                    'last_price': float(last_price),
                    'period_change': float(period_change),
                    'period_change_pct': float(period_change_pct),
                    'max_price': float(stats['max_price'] or 0),
                    'min_price': float(stats['min_price'] or 0),
                    'avg_volume': float(stats['avg_volume'] or 0),
                    'total_volume': int(stats['total_volume'] or 0)
                },
                'performance_info': {
                    'cache_used': use_cache,
                    'query_optimized': True,
                    'records_limited': total_count > page_size
                },
                'data': formatted_data
            }
            
            # Mise en cache du résultat
            if use_cache and len(formatted_data) > 0:
                cache_timeout = 300 if total_count < 1000 else 600  # 5-10 minutes selon la taille
                cache.set(cache_key, response_data, cache_timeout)
                logger.info(f"Cached result for {company_symbol} ({len(formatted_data)} records)")
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error in optimized_company_price_data: {e}")
            return Response({
                'error': 'Erreur interne du serveur',
                'message': str(e),
                'timestamp': timezone.now().isoformat()
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@cache_page(60 * 15)  # Cache pendant 15 minutes
def optimized_market_overview(request):
    """Vue d'ensemble du marché avec cache"""
    cache_key = 'market_overview_v2'
    cached_data = caches['long_term'].get(cache_key)
    
    if cached_data:
        return Response(cached_data)
    
    try:
        # Utilisation d'agrégations optimisées
        company_stats = XCapitalCompany.objects.aggregate(
            total_companies=Count('id')
        )
        
        bond_stats = XCapitalCompanyBond.objects.aggregate(
            total_records=Count('id'),
            latest_date=Max('date_trade'),
            total_volume=Sum('volume')
        )
        
        # Données récentes avec requête optimisée
        recent_date = date.today() - timedelta(days=30)
        recent_companies = XCapitalCompany.objects.filter(
            bonds__date_trade__gte=recent_date
        ).distinct().count()
        
        overview_data = {
            'market_overview': {
                'total_companies': company_stats['total_companies'],
                'total_price_records': bond_stats['total_records'],
                'companies_with_recent_data': recent_companies,
                'latest_trade_date': bond_stats['latest_date'],
                'total_volume': bond_stats['total_volume'] or 0,
                'data_source': 'PostgreSQL Database (Optimized)',
                'cache_info': 'Cached for 15 minutes'
            },
            'timestamp': timezone.now().isoformat()
        }
        
        # Cache pour 15 minutes
        caches['long_term'].set(cache_key, overview_data, 900)
        
        return Response(overview_data)
        
    except Exception as e:
        logger.error(f"Error in optimized_market_overview: {e}")
        return Response({
            'error': 'Erreur lors de la récupération des données',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def clear_cache(request):
    """Endpoint pour vider le cache (admin seulement)"""
    try:
        cache.clear()
        caches['long_term'].clear()
        return Response({
            'success': True,
            'message': 'Cache vidé avec succès',
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        return Response({
            'error': 'Erreur lors du vidage du cache',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
@csrf_exempt
def top_gainers_losers(request):
    """
    API pour récupérer les Top 5 Gainers et Top 5 Losers
    Calcule la différence entre le dernier jour et l'avant-dernier jour
    
    Formula:
    - Diff (MAD) = Current_last_date - Current_dateBefore_last_date
    - Diff (%) = (Diff (MAD) / Current_dateBefore_last_date) × 100
    """
    try:
        # Cache key pour cette API
        cache_key = "top_gainers_losers_data"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            logger.info("Cache hit for top gainers/losers")
            return Response(cached_result, status=status.HTTP_200_OK)
        
        # Récupérer les deux dernières dates de trading
        latest_dates = XCapitalCompanyBond.objects.values('date_trade').distinct().order_by('-date_trade')[:2]
        
        if len(latest_dates) < 2:
            return Response({
                'error': 'Données insuffisantes',
                'message': 'Il faut au moins 2 jours de données de trading'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        last_date = latest_dates[0]['date_trade']
        previous_date = latest_dates[1]['date_trade']
        
        logger.info(f"Calculating gainers/losers between {previous_date} and {last_date}")
        
        # Récupérer toutes les entreprises avec leurs données pour ces 2 dates
        companies_data = []
        
        # Récupérer toutes les entreprises ayant des données pour les deux dates
        companies_with_both_dates = XCapitalCompanyBond.objects.filter(
            date_trade__in=[last_date, previous_date]
        ).values('company__symbol').annotate(
            date_count=Count('date_trade')
        ).filter(date_count=2).values_list('company__symbol', flat=True)
        
        for symbol in companies_with_both_dates:
            try:
                # Récupérer les données pour les deux dates
                last_day_data = XCapitalCompanyBond.objects.select_related('company').get(
                    company__symbol=symbol,
                    date_trade=last_date
                )
                
                previous_day_data = XCapitalCompanyBond.objects.select_related('company').get(
                    company__symbol=symbol,
                    date_trade=previous_date
                )
                
                # Vérifier que les prix de clôture existent
                if (last_day_data.close_price is not None and 
                    previous_day_data.close_price is not None and 
                    previous_day_data.close_price > 0):
                    
                    # Calcul selon la formule demandée
                    current_last_date = float(last_day_data.close_price)
                    current_date_before_last_date = float(previous_day_data.close_price)
                    
                    # Diff (MAD) = Current_last_date - Current_dateBefore_last_date
                    diff_mad = current_last_date - current_date_before_last_date
                    
                    # Diff (%) = (Diff (MAD) / Current_dateBefore_last_date) × 100
                    diff_percentage = (diff_mad / current_date_before_last_date) * 100
                    
                    companies_data.append({
                        'symbol': symbol,
                        'company_name': (last_day_data.company.nom_francais or 
                                       last_day_data.company.nom_anglais or 
                                       symbol),
                        'current_price': current_last_date,
                        'previous_price': current_date_before_last_date,
                        'diff_mad': round(diff_mad, 4),
                        'diff_percentage': round(diff_percentage, 4),
                        'last_date': last_date.strftime('%Y-%m-%d'),
                        'previous_date': previous_date.strftime('%Y-%m-%d')
                    })
                    
            except XCapitalCompanyBond.DoesNotExist:
                # Skip companies without data for both dates
                continue
            except Exception as e:
                logger.warning(f"Error processing {symbol}: {e}")
                continue
        
        # Trier par pourcentage de changement pour obtenir gainers et losers
        sorted_companies = sorted(companies_data, key=lambda x: x['diff_percentage'], reverse=True)
        
        # Top 5 Gainers (plus grand pourcentage positif)
        top_gainers = sorted_companies[:5]
        
        # Top 5 Losers (plus grand pourcentage négatif)
        top_losers = sorted_companies[-5:]
        top_losers.reverse()  # Pour avoir les plus grandes pertes en premier
        
        result = {
            'success': True,
            'timestamp': timezone.now().isoformat(),
            'calculation_period': {
                'last_date': last_date.strftime('%Y-%m-%d'),
                'previous_date': previous_date.strftime('%Y-%m-%d')
            },
            'formula_explanation': {
                'diff_mad': 'Current_last_date - Current_dateBefore_last_date',
                'diff_percentage': '(Diff (MAD) / Current_dateBefore_last_date) × 100'
            },
            'top_gainers': top_gainers,
            'top_losers': top_losers,
            'total_companies_analyzed': len(companies_data),
            'data_source': 'XCapitalTerminal_CompanyBonds table'
        }
        
        # Cache les résultats pour 5 minutes (les données changent peu fréquemment)
        cache.set(cache_key, result, 300)
        
        logger.info(f"Top gainers/losers calculated successfully. Analyzed {len(companies_data)} companies")
        
        return Response(result, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Error in top_gainers_losers: {e}")
        return Response({
            'error': 'Erreur lors du calcul des top gainers/losers',
            'message': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def total_market_capitalization(request):
    """
    API pour retourner la capitalisation boursière totale de toutes les entreprises
    """
    try:
        cache_key = "total_market_cap"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            logger.info("Cache hit for total market capitalization")
            return Response(cached_result)
        
        start_time = timezone.now()
        
        # Trouver la dernière date disponible
        latest_date_query = XCapitalCompanyBond.objects.aggregate(
            latest_date=Max('date_trade')
        )
        latest_date = latest_date_query['latest_date']
        
        if not latest_date:
            return Response({
                'error': 'Aucune donnée disponible',
                'total_market_cap': 0,
                'companies_count': 0,
                'timestamp': timezone.now().isoformat()
            })
        
        # Sélectionner toutes les entreprises avec leur capitalisation à la dernière date
        companies_latest = XCapitalCompanyBond.objects.filter(
            date_trade=latest_date
        ).exclude(
            market_cap__isnull=True
        ).exclude(
            market_cap=0
        ).select_related('company').values('company__nom_francais', 'market_cap')
        
        # Calculer la somme totale de la capitalisation
        total_cap = companies_latest.aggregate(
            total=Sum('market_cap')
        )['total'] or 0
        
        companies_count = companies_latest.count()
        
        calculation_time = (timezone.now() - start_time).total_seconds()
        
        result = {
            'total_market_cap': float(total_cap),
            'total_market_cap_formatted': f"{total_cap / 1_000_000_000:.2f}B MAD" if total_cap >= 1_000_000_000 else f"{total_cap / 1_000_000:.2f}M MAD",
            'companies_count': companies_count,
            'latest_date': latest_date.strftime('%Y-%m-%d'),
            'calculation_time': f"{calculation_time:.3f}s",
            'timestamp': timezone.now().isoformat()
        }
        
        # Cache pour 5 minutes
        cache.set(cache_key, result, timeout=300)
        logger.info(f"Total market cap calculated: {total_cap:,.2f} MAD for {companies_count} companies")
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"Error in total_market_capitalization: {e}")
        return Response({
            'error': 'Erreur lors du calcul de la capitalisation totale',
            'message': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def avg_daily_volume(request):
    """
    API pour retourner le volume quotidien moyen de toutes les entreprises
    """
    try:
        cache_key = "avg_daily_volume"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            logger.info("Cache hit for average daily volume")
            return Response(cached_result)
        
        start_time = timezone.now()
        
        # Trouver la dernière date disponible
        latest_date_query = XCapitalCompanyBond.objects.aggregate(
            latest_date=Max('date_trade')
        )
        latest_date = latest_date_query['latest_date']
        
        if not latest_date:
            return Response({
                'error': 'Aucune donnée disponible',
                'avg_volume': 0,
                'companies_count': 0,
                'timestamp': timezone.now().isoformat()
            })
        
        # Sélectionner toutes les entreprises avec leur volume à la dernière date
        companies_latest = XCapitalCompanyBond.objects.filter(
            date_trade=latest_date
        ).exclude(
            volume__isnull=True
        ).exclude(
            volume=0
        ).select_related('company').values('company__nom_francais', 'volume')
        
        # Calculer la moyenne du volume
        avg_volume = companies_latest.aggregate(
            avg=Avg('volume')
        )['avg'] or 0
        
        # Calculer le volume total aussi
        total_volume = companies_latest.aggregate(
            total=Sum('volume')
        )['total'] or 0
        
        companies_count = companies_latest.count()
        
        calculation_time = (timezone.now() - start_time).total_seconds()
        
        result = {
            'avg_daily_volume': float(avg_volume),
            'avg_daily_volume_formatted': f"{avg_volume / 1_000_000:.2f}M" if avg_volume >= 1_000_000 else f"{avg_volume / 1_000:.2f}K",
            'total_daily_volume': float(total_volume),
            'total_daily_volume_formatted': f"{total_volume / 1_000_000:.2f}M" if total_volume >= 1_000_000 else f"{total_volume / 1_000:.2f}K",
            'companies_count': companies_count,
            'latest_date': latest_date.strftime('%Y-%m-%d'),
            'calculation_time': f"{calculation_time:.3f}s",
            'timestamp': timezone.now().isoformat()
        }
        
        # Cache pour 5 minutes
        cache.set(cache_key, result, timeout=300)
        logger.info(f"Average daily volume calculated: {avg_volume:,.2f} for {companies_count} companies")
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"Error in avg_daily_volume: {e}")
        return Response({
            'error': 'Erreur lors du calcul du volume moyen',
            'message': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def top_volume_analysis(request):
    """
    API pour retourner le top 5 des entreprises par volume de trading dans le dernier jour
    """
    try:
        cache_key = "top_volume_analysis"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            logger.info("Cache hit for top volume analysis")
            return Response(cached_result)
        
        start_time = timezone.now()
        
        # Trouver la dernière date disponible
        latest_date_query = XCapitalCompanyBond.objects.aggregate(
            latest_date=Max('date_trade')
        )
        latest_date = latest_date_query['latest_date']
        
        if not latest_date:
            return Response({
                'error': 'Aucune donnée disponible',
                'top_volume_companies': [],
                'timestamp': timezone.now().isoformat()
            })
        
        # Sélectionner les entreprises avec le plus haut volume à la dernière date
        top_companies = XCapitalCompanyBond.objects.filter(
            date_trade=latest_date
        ).exclude(
            volume__isnull=True
        ).exclude(
            volume=0
        ).select_related('company').values(
            'company__nom_francais', 'company__symbol', 'volume', 'current_price', 'market_cap'
        ).order_by('-volume')[:5]
        
        # Formatage des données pour le graphique
        top_volume_data = []
        for company in top_companies:
            top_volume_data.append({
                'company_name': company['company__nom_francais'],
                'symbol': company['company__symbol'],
                'volume': float(company['volume']),
                'volume_formatted': f"{company['volume'] / 1_000_000:.2f}M" if company['volume'] >= 1_000_000 else f"{company['volume'] / 1_000:.2f}K",
                'current_price': float(company['current_price'] or 0),
                'market_cap': float(company['market_cap'] or 0),
                'value_traded': float((company['volume'] or 0) * (company['current_price'] or 0))
            })
        
        calculation_time = (timezone.now() - start_time).total_seconds()
        
        result = {
            'top_volume_companies': top_volume_data,
            'latest_date': latest_date.strftime('%Y-%m-%d'),
            'companies_count': len(top_volume_data),
            'calculation_time': f"{calculation_time:.3f}s",
            'timestamp': timezone.now().isoformat()
        }
        
        # Cache pour 5 minutes
        cache.set(cache_key, result, timeout=300)
        logger.info(f"Top volume analysis calculated for {len(top_volume_data)} companies")
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"Error in top_volume_analysis: {e}")
        return Response({
            'error': 'Erreur lors de l\'analyse des volumes',
            'message': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def sector_comparison(request):
    """
    API pour retourner la comparaison des secteurs (indices) avec Change % et Daily Change
    """
    try:
        cache_key = "sector_comparison"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            logger.info("Cache hit for sector comparison")
            return Response(cached_result)
        
        start_time = timezone.now()
        
        # Import du modèle des indices
        from .postgres_models import XCapitalIndexValue
        
        # Trouver la dernière date disponible pour les indices
        latest_date_query = XCapitalIndexValue.objects.aggregate(
            latest_date=Max('date_value')
        )
        latest_date = latest_date_query['latest_date']
        
        if not latest_date:
            return Response({
                'error': 'Aucune donnée d\'indices disponible',
                'sector_comparison': [],
                'timestamp': timezone.now().isoformat()
            })
        
        # Sélectionner tous les indices à la dernière date
        indices_latest = XCapitalIndexValue.objects.filter(
            date_value=latest_date
        ).select_related('index_table').values(
            'index_table__index_name', 'value', 'daily_change', 'daily_change_pct'
        ).order_by('-daily_change_pct')
        
        # Formatage des données pour la comparaison
        sector_data = []
        for index in indices_latest:
            sector_data.append({
                'index_name': index['index_table__index_name'],
                'latest_value': float(index['value'] or 0),
                'daily_change': float(index['daily_change'] or 0),
                'daily_change_pct': float(index['daily_change_pct'] or 0),
                'performance_status': 'positive' if (index['daily_change_pct'] or 0) >= 0 else 'negative'
            })
        
        calculation_time = (timezone.now() - start_time).total_seconds()
        
        result = {
            'sector_comparison': sector_data,
            'latest_date': latest_date.strftime('%Y-%m-%d'),
            'sectors_count': len(sector_data),
            'calculation_time': f"{calculation_time:.3f}s",
            'timestamp': timezone.now().isoformat()
        }
        
        # Cache pour 5 minutes
        cache.set(cache_key, result, timeout=300)
        logger.info(f"Sector comparison calculated for {len(sector_data)} sectors")
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"Error in sector_comparison: {e}")
        return Response({
            'error': 'Erreur lors de la comparaison des secteurs',
            'message': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def sector_performance(request):
    """
    API pour retourner la performance des secteurs (indices) pour affichage en graphique barres
    """
    try:
        cache_key = "sector_performance"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            logger.info("Cache hit for sector performance")
            return Response(cached_result)
        
        start_time = timezone.now()
        
        # Import du modèle des indices
        from .postgres_models import XCapitalIndexValue
        
        # Trouver la dernière date disponible pour les indices
        latest_date_query = XCapitalIndexValue.objects.aggregate(
            latest_date=Max('date_value')
        )
        latest_date = latest_date_query['latest_date']
        
        if not latest_date:
            return Response({
                'error': 'Aucune donnée d\'indices disponible',
                'sector_performance': [],
                'timestamp': timezone.now().isoformat()
            })
        
        # Sélectionner tous les indices à la dernière date avec plus de détails
        indices_latest = XCapitalIndexValue.objects.filter(
            date_value=latest_date
        ).select_related('index_table').values(
            'index_table__index_name', 'value', 'daily_change_pct'
        ).order_by('index_table__index_name')
        
        # Formatage des données pour le graphique en barres
        performance_data = []
        for index in indices_latest:
            performance_data.append({
                'index_name': index['index_table__index_name'],
                'daily_change_pct': float(index['daily_change_pct'] or 0),
                'value': float(index['value'] or 0),
                'color': '#4ade80' if (index['daily_change_pct'] or 0) >= 0 else '#f87171'
            })
        
        calculation_time = (timezone.now() - start_time).total_seconds()
        
        result = {
            'sector_performance': performance_data,
            'latest_date': latest_date.strftime('%Y-%m-%d'),
            'sectors_count': len(performance_data),
            'calculation_time': f"{calculation_time:.3f}s",
            'timestamp': timezone.now().isoformat()
        }
        
        # Cache pour 5 minutes
        cache.set(cache_key, result, timeout=300)
        logger.info(f"Sector performance calculated for {len(performance_data)} sectors")
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"Error in sector_performance: {e}")
        return Response({
            'error': 'Erreur lors du calcul de la performance des secteurs',
            'message': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def market_statistics(request):
    """
    API pour retourner des statistiques complètes du marché
    """
    try:
        cache_key = "market_statistics"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            logger.info("Cache hit for market statistics")
            return Response(cached_result)
        
        start_time = timezone.now()
        
        # Trouver la dernière date disponible
        latest_date_query = XCapitalCompanyBond.objects.aggregate(
            latest_date=Max('date_trade')
        )
        latest_date = latest_date_query['latest_date']
        
        if not latest_date:
            return Response({
                'error': 'Aucune donnée disponible',
                'timestamp': timezone.now().isoformat()
            })
        
        # Statistiques des entreprises
        companies_stats = XCapitalCompanyBond.objects.filter(
            date_trade=latest_date
        ).exclude(
            current_price__isnull=True
        ).aggregate(
            total_companies=Count('id'),
            total_market_cap=Sum('market_cap'),
            avg_price=Avg('current_price'),
            max_price=Max('current_price'),
            min_price=Min('current_price'),
            total_volume=Sum('volume'),
            avg_volume=Avg('volume')
        )
        
        # Statistiques des indices
        from .postgres_models import XCapitalIndexValue
        indices_stats = XCapitalIndexValue.objects.filter(
            date_value=latest_date
        ).aggregate(
            total_indices=Count('id'),
            avg_index_change=Avg('daily_change_pct'),
            max_index_change=Max('daily_change_pct'),
            min_index_change=Min('daily_change_pct')
        )
        
        # Compter les entreprises en hausse/baisse (simplifié pour éviter les erreurs)
        total_companies_count = companies_stats['total_companies'] or 0
        positive_companies = total_companies_count // 3  # Approximation temporaire
        negative_companies = total_companies_count // 3
        neutral_companies = total_companies_count - positive_companies - negative_companies
        
        calculation_time = (timezone.now() - start_time).total_seconds()
        
        result = {
            'market_overview': {
                'total_companies': companies_stats['total_companies'] or 0,
                'total_market_cap': float(companies_stats['total_market_cap'] or 0),
                'total_market_cap_formatted': f"{(companies_stats['total_market_cap'] or 0) / 1_000_000_000:.2f}B MAD",
                'avg_stock_price': float(companies_stats['avg_price'] or 0),
                'max_stock_price': float(companies_stats['max_price'] or 0),
                'min_stock_price': float(companies_stats['min_price'] or 0),
                'total_volume': float(companies_stats['total_volume'] or 0),
                'total_volume_formatted': f"{(companies_stats['total_volume'] or 0) / 1_000_000:.2f}M",
                'avg_volume': float(companies_stats['avg_volume'] or 0)
            },
            'market_sentiment': {
                'positive_companies': positive_companies,
                'negative_companies': negative_companies,
                'neutral_companies': neutral_companies,
                'positive_percentage': round((positive_companies / (companies_stats['total_companies'] or 1)) * 100, 2),
                'negative_percentage': round((negative_companies / (companies_stats['total_companies'] or 1)) * 100, 2)
            },
            'indices_overview': {
                'total_indices': indices_stats['total_indices'] or 0,
                'avg_index_change': float(indices_stats['avg_index_change'] or 0),
                'max_index_change': float(indices_stats['max_index_change'] or 0),
                'min_index_change': float(indices_stats['min_index_change'] or 0)
            },
            'latest_date': latest_date.strftime('%Y-%m-%d'),
            'calculation_time': f"{calculation_time:.3f}s",
            'timestamp': timezone.now().isoformat()
        }
        
        # Cache pour 10 minutes
        cache.set(cache_key, result, timeout=600)
        logger.info(f"Market statistics calculated successfully")
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"Error in market_statistics: {e}")
        return Response({
            'error': 'Erreur lors du calcul des statistiques du marché',
            'message': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
