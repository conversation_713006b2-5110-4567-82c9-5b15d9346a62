from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal


class Stock(models.Model):
    """Model representing a stock/ticker"""
    ticker = models.CharField(max_length=20, unique=True, primary_key=True)
    name = models.CharField(max_length=200)
    sector = models.CharField(max_length=100, blank=True, null=True)
    market_cap = models.DecimalField(max_digits=20, decimal_places=2, blank=True, null=True)
    currency = models.CharField(max_length=10, default='MAD')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'stocks'
        ordering = ['ticker']

    def __str__(self):
        return f"{self.ticker} - {self.name}"


class StockPrice(models.Model):
    """Model representing historical stock price data"""
    stock = models.ForeignKey(Stock, on_delete=models.CASCADE, related_name='prices')
    date = models.DateField()
    open_price = models.DecimalField(max_digits=15, decimal_places=4, validators=[MinValueValidator(Decimal('0.0001'))])
    high_price = models.DecimalField(max_digits=15, decimal_places=4, validators=[MinValueValidator(Decimal('0.0001'))])
    low_price = models.DecimalField(max_digits=15, decimal_places=4, validators=[MinValueValidator(Decimal('0.0001'))])
    close_price = models.DecimalField(max_digits=15, decimal_places=4, validators=[MinValueValidator(Decimal('0.0001'))])
    volume = models.BigIntegerField(validators=[MinValueValidator(0)])
    adj_close = models.DecimalField(max_digits=15, decimal_places=4, blank=True, null=True)
    
    class Meta:
        db_table = 'stock_prices'
        unique_together = ['stock', 'date']
        ordering = ['-date']
        indexes = [
            models.Index(fields=['stock', 'date']),
            models.Index(fields=['date']),
        ]

    def __str__(self):
        return f"{self.stock.ticker} - {self.date}"

    @property
    def daily_return(self):
        """Calculate daily return percentage"""
        if self.open_price > 0:
            return float((self.close_price - self.open_price) / self.open_price * 100)
        return 0.0


class MarketIndex(models.Model):
    """Model representing market indices like MASI, MADEX, etc."""
    code = models.CharField(max_length=20, unique=True, primary_key=True)
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    currency = models.CharField(max_length=10, default='MAD')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'market_indices'
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"


class IndexPrice(models.Model):
    """Model representing historical index price data"""
    index = models.ForeignKey(MarketIndex, on_delete=models.CASCADE, related_name='prices')
    date = models.DateField()
    open_value = models.DecimalField(max_digits=15, decimal_places=4)
    high_value = models.DecimalField(max_digits=15, decimal_places=4)
    low_value = models.DecimalField(max_digits=15, decimal_places=4)
    close_value = models.DecimalField(max_digits=15, decimal_places=4)
    volume = models.BigIntegerField(default=0)
    
    class Meta:
        db_table = 'index_prices'
        unique_together = ['index', 'date']
        ordering = ['-date']
        indexes = [
            models.Index(fields=['index', 'date']),
            models.Index(fields=['date']),
        ]

    def __str__(self):
        return f"{self.index.code} - {self.date}"


class TradingSession(models.Model):
    """Model representing trading session information"""
    date = models.DateField(unique=True)
    is_trading_day = models.BooleanField(default=True)
    session_start = models.TimeField(blank=True, null=True)
    session_end = models.TimeField(blank=True, null=True)
    total_volume = models.BigIntegerField(default=0)
    total_value = models.DecimalField(max_digits=20, decimal_places=2, default=0)
    notes = models.TextField(blank=True)
    
    class Meta:
        db_table = 'trading_sessions'
        ordering = ['-date']

    def __str__(self):
        return f"Trading Session - {self.date}"
