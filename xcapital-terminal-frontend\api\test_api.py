#!/usr/bin/env python3
"""
Test script for XCapital Terminal ARIMA API
"""

import requests
import json
from datetime import datetime, timedelta

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"

def test_api_health():
    """Test if the API is running"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ API Health Check: PASSED")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ API Health Check: FAILED (Status: {response.status_code})")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ API Health Check: FAILED (Error: {e})")
        return False

def test_get_tickers():
    """Test getting available tickers"""
    try:
        response = requests.get(f"{API_BASE_URL}/tickers", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ Get Tickers: PASSED")
            print(f"   Available tickers: {data.get('count', 0)}")
            if data.get('tickers'):
                print(f"   Sample tickers: {data['tickers'][:5]}")
            return data.get('tickers', [])
        else:
            print(f"❌ Get Tickers: FAILED (Status: {response.status_code})")
            return []
    except requests.exceptions.RequestException as e:
        print(f"❌ Get Tickers: FAILED (Error: {e})")
        return []

def test_arima_prediction(ticker):
    """Test ARIMA prediction for a specific ticker"""
    try:
        # Use recent dates
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
        
        params = {
            'ticker': ticker,
            'start_date': start_date,
            'end_date': end_date,
            'forecast_days': 10
        }
        
        response = requests.get(f"{API_BASE_URL}/arima-predict", params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ ARIMA Prediction for {ticker}: PASSED")
            print(f"   Model type: {data.get('model_type')}")
            print(f"   Forecast days: {len(data.get('forecast_values', []))}")
            print(f"   Test predictions: {len(data.get('test_predictions', []))}")
            if data.get('model_metrics'):
                print(f"   RMSE: {data['model_metrics'].get('rmse', 'N/A')}")
            return True
        else:
            print(f"❌ ARIMA Prediction for {ticker}: FAILED (Status: {response.status_code})")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   Error: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ ARIMA Prediction for {ticker}: FAILED (Error: {e})")
        return False

def main():
    print("🧪 Testing XCapital Terminal ARIMA API Integration")
    print("=" * 60)
    
    # Test 1: Health check
    if not test_api_health():
        print("\n💡 Make sure the API server is running:")
        print("   cd api && python main.py")
        return
    
    print()
    
    # Test 2: Get available tickers
    tickers = test_get_tickers()
    if not tickers:
        print("\n💡 Make sure ARIMA model files are in the models/Arima directory")
        return
    
    print()
    
    # Test 3: Test ARIMA prediction with first available ticker
    if tickers:
        test_ticker = tickers[0]
        print(f"Testing ARIMA prediction with ticker: {test_ticker}")
        success = test_arima_prediction(test_ticker)
        
        if success:
            print("\n🎉 All tests passed! ARIMA integration is working correctly.")
            print("\n🚀 You can now test the full interface at:")
            print("   http://localhost:3000")
        else:
            print(f"\n💡 Check if historical data exists for {test_ticker} in the data directory")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
