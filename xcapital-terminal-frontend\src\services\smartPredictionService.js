import axios from 'axios';

// Model configuration and capabilities
export const MODEL_CONFIGS = {
    ARIMA: {
        name: "ARIMA",
        endpoint: "/arima-predict",
        strengths: ["price_forecast", "trend_analysis"],
        timeHorizons: ["short", "medium"],
        marketConditions: ["stable", "trending"],
        description: "Statistical time series model for price forecasting with trend analysis",
        features: {
            confidence_intervals: true,
            in_sample_testing: true,
            forecast_horizon: 30,
            requires_historical_data: true
        }
    },
    GARCH: {
        name: "GARCH", 
        endpoint: "/garch-predict",
        strengths: ["volatility_analysis", "risk_assessment"],
        timeHorizons: ["short", "medium"],
        marketConditions: ["volatile", "uncertain"],
        description: "Specialized in volatility forecasting and risk assessment"
    },
    ARIMAX: {
        name: "ARIMAX",
        endpoint: "/arimax-predict", 
        strengths: ["economic_impact", "price_forecast"],
        timeHorizons: ["medium", "long"],
        marketConditions: ["stable"],
        description: "Incorporates external economic factors for enhanced predictions"
    },
    LSTM: {
        name: "LSTM",
        endpoint: "/lstm-predict",
        strengths: ["price_forecast", "trend_analysis"],
        timeHorizons: ["medium", "long"],
        marketConditions: ["volatile", "trending"],
        description: "Deep learning approach for complex pattern recognition"
    },
    XGBoost: {
        name: "XGBoost",
        endpoint: "/xgboost-predict",
        strengths: ["price_forecast", "trend_analysis"],
        timeHorizons: ["short", "medium"],
        marketConditions: ["trending"],
        description: "Machine learning for non-linear pattern detection"
    },
    "Monte Carlo": {
        name: "Monte Carlo",
        endpoint: "/monte-carlo-simulate",
        strengths: ["risk_assessment", "options_pricing"],
        timeHorizons: ["long"],
        marketConditions: ["volatile", "uncertain"],
        description: "Simulation-based risk and scenario analysis"
    },
    BSM: {
        name: "BSM",
        endpoint: "/bsm-price",
        strengths: ["options_pricing"],
        timeHorizons: ["short", "medium"],
        marketConditions: ["stable"],
        description: "Classical options pricing model"
    },
    "ARIMA-LSTM": {
        name: "ARIMA-LSTM",
        endpoint: "/arima-lstm-hybrid",
        strengths: ["price_forecast"],
        timeHorizons: ["long"],
        marketConditions: ["trending"],
        description: "Hybrid approach combining statistical and deep learning"
    }
};

export class SmartModelSelector {
    static selectBestModel(predictionType, timeHorizon, marketCondition) {
        const candidates = [];
        
        // Score each model based on how well it matches the criteria
        Object.entries(MODEL_CONFIGS).forEach(([modelName, config]) => {
            let score = 0;
            
            // Primary strength match (highest weight)
            if (config.strengths.includes(predictionType)) {
                score += 10;
            }
            
            // Time horizon match
            if (config.timeHorizons.includes(timeHorizon)) {
                score += 5;
            }
            
            // Market condition match
            if (config.marketConditions.includes(marketCondition)) {
                score += 3;
            }
            
            if (score > 0) {
                candidates.push({ modelName, score, config });
            }
        });
        
        // Sort by score and return the best match
        candidates.sort((a, b) => b.score - a.score);
        
        if (candidates.length > 0) {
            return candidates[0].modelName;
        }
        
        // Fallback to ARIMA if no good match found
        return "ARIMA";
    }
    
    static getModelDescription(modelName) {
        return MODEL_CONFIGS[modelName]?.description || "Advanced prediction model";
    }
    
    static getAvailableModels(predictionType) {
        return Object.entries(MODEL_CONFIGS)
            .filter(([_, config]) => config.strengths.includes(predictionType))
            .map(([modelName, config]) => ({ modelName, ...config }));
    }
}

export class PredictionAPIService {
    static baseUrl = process.env.REACT_APP_PREDICTION_API_URL || "http://localhost:8000/api/v1";
    
    // Fallback model hierarchy for each prediction type
    static fallbackModels = {
        "price_forecast": ["ARIMA", "ARIMAX", "LSTM", "XGBoost"],
        "volatility_analysis": ["GARCH", "ARIMA", "Monte Carlo"],
        "trend_analysis": ["ARIMA", "XGBoost", "LSTM"],
        "risk_assessment": ["GARCH", "Monte Carlo", "ARIMA"],
        "options_pricing": ["BSM", "Monte Carlo", "GARCH"],
        "economic_impact": ["ARIMAX", "ARIMA", "LSTM"]
    };
    
    static async makePrediction(modelName, parameters) {
        const predictionType = this.getPredictionTypeFromModel(modelName, parameters);
        const fallbacks = this.fallbackModels[predictionType] || ["ARIMA"];
        
        // Try the primary model first
        if (fallbacks.includes(modelName)) {
            try {
                return await this.tryModel(modelName, parameters);
            } catch (error) {
                console.warn(`Primary model ${modelName} failed:`, error.message);
                // Try fallback models
                return await this.tryFallbackModels(fallbacks, modelName, parameters, error);
            }
        } else {
            // If primary model not in fallback list, try it anyway
            try {
                return await this.tryModel(modelName, parameters);
            } catch (error) {
                console.warn(`Model ${modelName} failed:`, error.message);
                return await this.tryFallbackModels(fallbacks, modelName, parameters, error);
            }
        }
    }
    
    static async tryFallbackModels(fallbacks, primaryModel, parameters, primaryError) {
        const modelsToTry = fallbacks.filter(model => model !== primaryModel && MODEL_CONFIGS[model]);
        
        for (const fallbackModel of modelsToTry) {
            try {
                console.log(`Trying fallback model: ${fallbackModel}`);
                const result = await this.tryModel(fallbackModel, parameters);
                result.usedFallback = true;
                result.fallbackModel = fallbackModel;
                result.primaryModel = primaryModel;
                result.primaryError = primaryError.message;
                return result;
            } catch (fallbackError) {
                console.warn(`Fallback model ${fallbackModel} also failed:`, fallbackError.message);
                continue;
            }
        }
        
        // If all models fail, throw the original error
        throw new Error(`All prediction models failed. Primary error: ${primaryError.message}. Please check backend services or try again later.`);
    }
    
    static getPredictionTypeFromModel(modelName, parameters) {
        // Try to infer prediction type from model strengths
        const config = MODEL_CONFIGS[modelName];
        return config?.strengths[0] || "price_forecast";
    }
    
    static async tryModel(modelName, parameters) {
        const config = MODEL_CONFIGS[modelName];
        if (!config) {
            throw new Error(`Unknown model: ${modelName}`);
        }

        const { ticker, startDate, endDate, externalFactors = [] } = parameters;
        let url = `${this.baseUrl}${config.endpoint}?ticker=${ticker}&start_date=${startDate}&end_date=${endDate}`;
        
        // Add model-specific parameters
        if (modelName === "ARIMAX" && externalFactors.length > 0) {
            url += `&list_exog=${externalFactors.join(",")}`;
        }
        
        // Add forecast days for ARIMA
        if (modelName === "ARIMA") {
            url += `&forecast_days=30`;
        }

        try {
            const response = await axios.get(url, {
                timeout: 30000, // 30 second timeout
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });
            const result = this.formatResponse(modelName, response.data);
            result.modelUsed = modelName;
            return result;
        } catch (error) {
            console.error(`Error calling ${modelName} API:`, error);
            
            // Provide more specific error messages
            if (error.response) {
                const status = error.response.status;
                const message = error.response.data?.message || error.response.statusText;
                
                switch (status) {
                    case 400:
                        throw new Error(`Invalid parameters for ${modelName}: ${message}`);
                    case 404:
                        throw new Error(`${modelName} service not available`);
                    case 500:
                        throw new Error(`${modelName} service temporarily unavailable`);
                    case 503:
                        throw new Error(`${modelName} service under maintenance`);
                    default:
                        throw new Error(`${modelName} service error (${status}): ${message}`);
                }
            } else if (error.request) {
                throw new Error(`Cannot connect to ${modelName} service`);
            } else {
                throw new Error(`${modelName} configuration error: ${error.message}`);
            }
        }
    }
    
    static formatResponse(modelName, data) {
        switch (modelName) {
            case "GARCH":
                return {
                    type: "volatility",
                    data: {
                        labels: Array.from({ length: data.forecasted_volatility?.length || 0 }, (_, i) => i + 1),
                        values: data.forecasted_volatility || [],
                        title: "Volatility Forecast"
                    }
                };
                
            case "ARIMA":
                // Handle new ARIMA response format
                if (data.test_dates && data.test_actuals && data.test_predictions) {
                    const combined = data.test_dates.map((date, index) => ({
                        date,
                        actual: data.test_actuals[index],
                        predicted: data.test_predictions[index],
                    })).sort((a, b) => new Date(a.date) - new Date(b.date));
                    
                    return {
                        type: "price_prediction",
                        data: {
                            labels: combined.map(item => new Date(item.date).toLocaleDateString()),
                            actual: combined.map(item => item.actual),
                            predicted: combined.map(item => item.predicted),
                            title: "ARIMA Price Prediction",
                            // Add forecast data
                            forecast: {
                                dates: data.forecast_dates || [],
                                values: data.forecast_values || [],
                                confidence_lower: data.confidence_lower || [],
                                confidence_upper: data.confidence_upper || []
                            },
                            metrics: data.model_metrics || {}
                        }
                    };
                }
                // Fallback for old format
                return {
                    type: "price_prediction",
                    data: {
                        labels: data.forecast_dates || [],
                        values: data.forecast_values || [],
                        title: "ARIMA Forecast"
                    }
                };
                
            case "ARIMAX":
            case "LSTM":
            case "XGBoost":
            case "ARIMA-LSTM":
                if (data.test_dates && data.test_actuals && data.test_predictions) {
                    const combined = data.test_dates.map((date, index) => ({
                        date,
                        actual: data.test_actuals[index],
                        predicted: data.test_predictions[index],
                    })).sort((a, b) => new Date(a.date) - new Date(b.date));
                    
                    return {
                        type: "price_prediction",
                        data: {
                            labels: combined.map(item => new Date(item.date).toLocaleDateString()),
                            actual: combined.map(item => item.actual),
                            predicted: combined.map(item => item.predicted),
                            title: "Price Prediction"
                        }
                    };
                }
                break;
                
            case "Monte Carlo":
                return {
                    type: "simulation",
                    data: {
                        scenarios: data.scenarios || [],
                        statistics: data.statistics || {},
                        title: "Risk Scenarios"
                    }
                };
                
            case "BSM":
                return {
                    type: "options_pricing",
                    data: {
                        callPrice: data.call_price || 0,
                        putPrice: data.put_price || 0,
                        greeks: data.greeks || {},
                        title: "Options Pricing"
                    }
                };
                
            default:
                return {
                    type: "unknown",
                    data: data
                };
        }
    }
}

// Prediction type definitions with user-friendly descriptions
export const PREDICTION_TYPES = [
    {
        id: "price_forecast",
        label: "Equity Price Modeling",
        description: "Advanced time-series analysis for price trajectory prediction",
        icon: "PRICE",
        userQuestions: [
            "What is the expected price trajectory for the next trading period?",
            "What are the probabilistic price targets?",
            "What is the optimal price range for this security?"
        ]
    },
    {
        id: "volatility_analysis", 
        label: "Volatility Surface Analysis",
        description: "Comprehensive volatility modeling and risk metrics",
        icon: "VOL",
        userQuestions: [
            "What is the implied volatility structure?",
            "What are the volatility clustering patterns?",
            "What is the expected volatility regime?"
        ]
    },
    {
        id: "trend_analysis",
        label: "Momentum & Trend Analytics", 
        description: "Statistical trend identification and momentum analysis",
        icon: "TREND",
        userQuestions: [
            "What are the underlying momentum patterns?",
            "What is the probability of trend continuation?",
            "What are the key technical resistance levels?"
        ]
    },
    {
        id: "risk_assessment",
        label: "Portfolio Risk Analytics",
        description: "Comprehensive risk assessment and stress testing",
        icon: "RISK",
        userQuestions: [
            "What is the Value-at-Risk for this position?",
            "What are the tail risk scenarios?",
            "What is the maximum drawdown potential?"
        ]
    },
    {
        id: "options_pricing",
        label: "Derivatives Valuation",
        description: "Sophisticated options pricing and Greeks analysis",
        icon: "OPT",
        userQuestions: [
            "What is the theoretical fair value of this derivative?",
            "What are the sensitivity parameters?",
            "What is the optimal hedging strategy?"
        ]
    },
    {
        id: "economic_impact",
        label: "Macroeconomic Impact Analysis",
        description: "Economic factor analysis and systematic risk assessment",
        icon: "MACRO",
        userQuestions: [
            "How do macroeconomic factors impact this security?",
            "What's the impact of inflation on this company?",
            "How sensitive is this stock to economic indicators?"
        ]
    }
];

const smartPredictionService = {
    MODEL_CONFIGS,
    SmartModelSelector,
    PredictionAPIService,
    PREDICTION_TYPES
};

export default smartPredictionService;
