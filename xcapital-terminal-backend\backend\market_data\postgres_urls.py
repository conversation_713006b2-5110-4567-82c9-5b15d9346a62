"""
URLs pour les APIs XCapital utilisant PostgreSQL
"""
from django.urls import path, re_path
from . import postgres_views

app_name = 'xcapital_data'

urlpatterns = [
    # =========================
    # ENTREPRISES XCAPITAL
    # =========================
    
    # Liste et détails des entreprises
    path('companies/', postgres_views.simple_companies_list, name='companies-list-simple'),
    path('companies/full/', postgres_views.XCapitalCompanyListView.as_view(), name='companies-list-full'),
    path('companies/<str:symbol>/', postgres_views.XCapitalCompanyDetailView.as_view(), name='company-detail'),
    
    # Prix des entreprises (bonds)
    path('companies/<str:symbol>/prices/', postgres_views.XCapitalCompanyBondListView.as_view(), name='company-prices'),
    path('companies/<str:symbol>/chart/', postgres_views.get_xcapital_chart_data, name='company-chart'),
    path('companies/<str:symbol>/variations/', postgres_views.get_xcapital_price_variations, name='company-variations'),
    
    # API POST pour récupérer des données via formulaire
    path('companies/form-data/', postgres_views.get_xcapital_data_form, name='company-data-form'),
    # Route tolérante pour URLs avec espaces encodés (%20) ou espaces finaux accidentels
    re_path(r'^companies/form-data/\s*$', postgres_views.get_xcapital_data_form),
    
    # API POST sécurisée pour les données de prix (2022-08-22 à aujourd'hui par défaut)
    path('secure/company-data/', postgres_views.clean_company_price_data, name='secure-company-data'),
    path('clean/company-data/', postgres_views.clean_company_price_data, name='clean-company-data'),
    
    # =========================
    # ANALYSES ET VUES D'ENSEMBLE
    # =========================
    
    # Vue d'ensemble du marché
    path('market/overview/', postgres_views.get_xcapital_market_overview, name='market-overview'),
    
    # Symboles disponibles
    path('symbols/', postgres_views.get_xcapital_available_symbols, name='available-symbols'),
    
    # Liste des entreprises disponibles
    path('available-companies/', postgres_views.available_companies_list, name='available-companies'),
    
    # =========================
    # INDICES XCAPITAL
    # =========================
    
    # Liste et détails des indices
    path('indices/', postgres_views.simple_indices_list, name='indices-list-simple'),
    path('indices/full/', postgres_views.XCapitalIndexListView.as_view(), name='indices-list-full'),
    
    # API POST pour récupérer des données d'indices via formulaire - DOIT ÊTRE AVANT indices/<str:index_id>/
    path('indices/form-data/', postgres_views.get_xcapital_index_data_form, name='index-data-form'),
    
    path('indices/<str:index_id>/', postgres_views.XCapitalIndexDetailView.as_view(), name='index-detail'),
    
    # Valeurs des indices
    path('indices/<str:index_id>/values/', postgres_views.XCapitalIndexValueListView.as_view(), name='index-values'),
    path('indices/<str:index_id>/chart/', postgres_views.get_xcapital_index_chart_data, name='index-chart'),
    path('indices/<str:index_id>/variations/', postgres_views.get_xcapital_index_variations, name='index-variations'),
    
    # Indices disponibles
    path('available-indices/', postgres_views.get_xcapital_available_indices, name='available-indices'),
    
    # Liste des indices disponibles (avec détails complets)
    path('indices-list/', postgres_views.available_indices_list, name='indices-available'),
]
