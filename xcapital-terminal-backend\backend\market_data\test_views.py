from rest_framework.decorators import api_view
from rest_framework.response import Response
from django.views.decorators.csrf import csrf_exempt

@csrf_exempt
@api_view(['GET', 'POST'])
def test_simple_post(request):
    """Test simple pour vérifier si POST fonctionne"""
    if request.method == 'GET':
        return Response({
            'message': 'Test GET fonctionne',
            'methods_allowed': ['GET', 'POST']
        })
    elif request.method == 'POST':
        return Response({
            'message': 'Test POST fonctionne!',
            'data_received': request.data,
            'method': request.method
        })
    else:
        return Response({
            'error': f'Method {request.method} not allowed'
        }, status=405)
