import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
    Box,
    VStack,
    HStack,
    Text,
    Card,
    CardBody,
    CardHeader,
    Flex,
    Button,
    Grid,
    GridItem,
    Tabs,
    TabList,
    TabPanels,
    Tab,
    TabPanel,
    Stat,
    StatLabel,
    StatNumber,
    StatHelpText,
    StatArrow,
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    TableContainer,
    IconButton,
    useToast
} from '@chakra-ui/react';
import { Line, Bar } from 'react-chartjs-2';
import {
    Chart as ChartJS,
    LineElement,
    PointElement,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip as ChartTooltip,
    Legend,
    ArcElement
} from 'chart.js';
import MasiIndicesVisualization from './MasiIndicesVisualization';
import { FiRefreshCw, FiDownload, FiTrendingUp, FiTrendingDown } from 'react-icons/fi';
// TEMPORARILY COMMENTED OUT FOR BACKEND TESTING - Uncomment after verification
// import dataVisualizationService from '../../../services/dataVisualizationService';
import xcapitalBackendService from '../../../services/xcapitalBackendService';

// Register Chart.js components
ChartJS.register(
    LineElement,
    PointElement,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    ChartTooltip,
    Legend,
    ArcElement
);

const Dashboard = () => {
    // State management
    const [activeTab, setActiveTab] = useState(0);
    
    // Data states
    const [dailyData, setDailyData] = useState({
        indices: [],
        hausses: [],
        baisses: []
    });
    
    // Chart data states
    const [marketTrendData, setMarketTrendData] = useState(null);
    const [volumeData, setVolumeData] = useState(null);
    
    // New comprehensive data states
    const [topGainers, setTopGainers] = useState([]);
    const [topLosers, setTopLosers] = useState([]);
    const [marketOverview, setMarketOverview] = useState(null);
    const [isLoading, setIsLoading] = useState(false);

    const toast = useToast();

    // Memoized arrays to prevent useEffect dependency warnings
    const sectors = useMemo(() => [
        'MASI_AGROALIMENTAIRE',
        'MASI_AUTOMOBILE',
        'MASI_BOISSONS',
        'MASI_CHIMIE',
        'MASI_EQUIPEMENT_ELECTRONIQUE_ET_ELECTRIQUE',
        'MASI_IMMOBILIER',
        'MASI_LOISIRS_ET_HOTELS',
        'MASI_MATERIELS_ET_LOGICIELS_INFORMATIQUES',
        'MASI_NTI',
        'MASI_PARTICIPATION_ET_PROMOTION_IMMOBILIERES',
        'MASI_PETROLE_ET_GAZ',
        'MASI_SANTE',
        'MASI_SERVICES_DE_TRANSPORT',
        'MASI_SOCIETES_DE_PORTEFEUILLE_HOLDINGS',
        'MASI_SOCIETE_DE_FINANCEMENT_ET_AUTRES_ACTIVITES_FINANCIERES',
        'MASI_SYLVICULTURE_ET_PAPIER',
        'MASI_TELECOMMUNICATIONS',
        'MASI_TEXTILE'
    ], []);

    // Enhanced data loading functions - Using comprehensive backend APIs
    const loadDailyData = useCallback(async () => {
        try {
            // Use the comprehensive dashboard API for all data
            const data = await xcapitalBackendService.getComprehensiveDashboardData();
            console.log('✅ SUCCESS: Comprehensive dashboard data loaded:', data);
            
            // Set all the different data states
            setDailyData({
                indices: data.indices || [],
                hausses: data.topGainers || [],
                baisses: data.topLosers || [],
                summary: {
                    total_companies: data.marketOverview?.market_overview?.total_companies || 0,
                    gainers_count: data.topGainers?.length || 0,
                    losers_count: data.topLosers?.length || 0,
                    unchanged: 0
                }
            });
            
            // Set market trend data
            if (data.performanceTrend) {
                setMarketTrendData(data.performanceTrend);
            }
            
            // Set volume data
            if (data.volumeAnalysis) {
                setVolumeData(data.volumeAnalysis);
            }
            
        } catch (error) {
            console.error('❌ BACKEND FAILED - Comprehensive Data:', error);
            // FALLBACK COMMENTED OUT FOR TESTING - Uncomment after backend verification
            // try {
            //     const fallbackData = await dataVisualizationService.loadDailyData();
            //     console.log('Using fallback data:', fallbackData);
            //     setDailyData(fallbackData);
            // } catch (fallbackError) {
            //     console.error('Fallback data loading also failed:', fallbackError);
                toast({
                    title: "Backend Connection Failed",
                    description: "Please start the Django backend server (localhost:8000)",
                    status: "error",
                    duration: 8000,
                    isClosable: true,
                });
            // }
        }
    }, [toast]);

    // Load chart data - Now using real backend APIs
    const loadChartData = useCallback(async () => {
        try {
            // Load market trends from MASI index
            const loadMasiTrendData = async () => {
                try {
                    const trendData = await xcapitalBackendService.getMarketTrendsData('MASI', '30D');
                    if (trendData) {
                        console.log('Loaded MASI trend data from backend:', trendData);
                        return trendData;
                    }
                    // Fallback to CSV if backend fails
                    return await loadMasiTrendDataFromCSV();
                } catch (error) {
                    console.error('Error loading MASI trend data from backend:', error);
                    return await loadMasiTrendDataFromCSV();
                }
            };

            // Fallback function for CSV data
            const loadMasiTrendDataFromCSV = async () => {
                try {
                    const response = await fetch('/data_visualisation/csv/MASI.csv');
                    const csvText = await response.text();
                    const data = parseCSV(csvText);
                    
                    // Get last 30 data points
                    const last30Days = data.slice(-30);
                    
                    return {
                        labels: last30Days.map(item => new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })),
                        datasets: [{
                            label: 'MASI Index',
                            data: last30Days.map(item => parseFloat(item.value)),
                            borderColor: '#ffce30',
                            backgroundColor: 'rgba(255, 206, 48, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 2,
                            pointHoverRadius: 6
                        }]
                    };
                } catch (error) {
                    console.error('❌ BACKEND FAILED - MASI trend data:', error);
                    // FALLBACK COMMENTED OUT FOR TESTING - Uncomment after backend verification
                    // return generateMockTrendData();
                    return null;
                }
            };

            // Load volume data from backend
            const loadVolumeData = async () => {
                try {
                    const volumeData = await xcapitalBackendService.getTopTradingVolumes(7);
                    if (volumeData) {
                        console.log('Loaded volume data from backend:', volumeData);
                        return volumeData;
                    }
                    // Fallback to daily data processing
                    return loadVolumeDataFromDailyData();
                } catch (error) {
                    console.error('Error loading volume data from backend:', error);
                    return loadVolumeDataFromDailyData();
                }
            };

            // Fallback function for volume data from daily data
            const loadVolumeDataFromDailyData = () => {
                try {
                    // Use volume data from daily hausses and baisses data
                    const allStocks = [...dailyData.hausses, ...dailyData.baisses];
                    console.log('Volume analysis - All stocks:', allStocks);
                    
                    // Sort by volume and take top 7
                    const sortedByVolume = allStocks
                        .filter(stock => stock.volume_echange && parseFloat(stock.volume_echange) > 0)
                        .sort((a, b) => parseFloat(b.volume_echange) - parseFloat(a.volume_echange))
                        .slice(0, 7);
                    
                    console.log('Volume analysis - Top stocks:', sortedByVolume);
                    
                    const labels = sortedByVolume.map(stock => stock.libelle?.substring(0, 15) + '...' || 'Unknown');
                    const volumes = sortedByVolume.map(stock => parseFloat(stock.volume_echange) / 1000000);
                    
                    console.log('Volume chart data:', { labels, volumes });
                    
                    return {
                        labels,
                        datasets: [{
                            label: 'Trading Volume (Million MAD)',
                            data: volumes,
                            backgroundColor: [
                                '#ffce30', '#4ade80', '#f87171', '#60a5fa', 
                                '#a78bfa', '#fb7185', '#34d399'
                            ],
                            borderColor: '#333',
                            borderWidth: 1
                        }]
                    };
                } catch (error) {
                    console.error('❌ BACKEND FAILED - Volume data:', error);
                    // FALLBACK COMMENTED OUT FOR TESTING - Uncomment after backend verification
                    // return generateMockVolumeData();
                    return null;
                }
            };

            const [trendData, volData] = await Promise.all([
                loadMasiTrendData(),
                loadVolumeData()
            ]);
            
            setMarketTrendData(trendData);
            setVolumeData(volData);
        } catch (error) {
            console.error('❌ BACKEND FAILED - Chart data loading:', error);
            toast({
                title: "Backend Connection Required",
                description: "Please start the Django backend server to load chart data.",
                status: "error",
                duration: 8000,
                isClosable: true,
            });
        }
    }, [dailyData.hausses, dailyData.baisses, toast]);

    // Helper function to parse CSV data
    const parseCSV = (csvText) => {
        const lines = csvText.trim().split('\n');
        const headers = lines[0].split(',');
        
        return lines.slice(1).map(line => {
            const values = line.split(',');
            const obj = {};
            headers.forEach((header, index) => {
                obj[header.trim()] = values[index]?.trim() || '';
            });
            return obj;
        });
    };

    // TEMPORARILY COMMENTED OUT FOR BACKEND TESTING - Uncomment after verification
    /*
    const generateMockTrendData = () => {
        const days = 30;
        const labels = [];
        const data = [];
        const baseValue = 13000;
        
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
            
            const variance = (Math.random() - 0.5) * 200;
            const trendValue = baseValue + variance + (i * 5);
            data.push(trendValue);
        }
        
        return {
            labels,
            datasets: [{
                label: 'MASI Index',
                data,
                borderColor: '#ffce30',
                backgroundColor: 'rgba(255, 206, 48, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4,
                pointRadius: 2,
                pointHoverRadius: 6
            }]
        };
    };
    */

    // TEMPORARILY COMMENTED OUT FOR BACKEND TESTING - Uncomment after verification
    /*
    const generateMockVolumeData = () => {
        const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        const volumes = days.map(() => Math.floor(Math.random() * 500000) + 100000);
        
        return {
            labels: days,
            datasets: [{
                label: 'Trading Volume (MAD)',
                data: volumes,
                backgroundColor: [
                    '#ffce30', '#4ade80', '#f87171', '#60a5fa',
                    '#a78bfa', '#fb7185', '#34d399'
                ],
                borderColor: '#333',
                borderWidth: 1
            }]
        };
    };
    */

    // Load real data on component mount - Now using backend APIs
    useEffect(() => {
        console.log('Dashboard initializing with XCapital backend APIs...');
        loadDailyData();
    }, [loadDailyData]);

    // Load chart data when daily data changes - Enhanced with backend integration
    useEffect(() => {
        if (dailyData.hausses.length > 0 || dailyData.baisses.length > 0) {
            console.log('Loading chart data with backend APIs...');
            loadChartData();
        }
    }, [dailyData, loadChartData]);

    // Refresh handler that explicitly uses backend APIs
    const handleRefresh = useCallback(async () => {
        try {
            console.log('Refreshing data from XCapital backend APIs...');
            
            toast({
                title: "Refreshing Data",
                description: "Loading latest market data from XCapital backend...",
                status: "info",
                duration: 3000,
                isClosable: true,
            });

            // Force refresh from backend APIs
            await loadDailyData();
            
            // Small delay to ensure daily data is loaded before chart data
            setTimeout(() => {
                loadChartData();
            }, 1000);

            toast({
                title: "Data Refreshed",
                description: "Market data updated successfully from backend APIs.",
                status: "success",
                duration: 3000,
                isClosable: true,
            });
        } catch (error) {
            console.error('Error refreshing data:', error);
            toast({
                title: "Refresh Failed",
                description: "Failed to refresh data from backend. Please try again.",
                status: "error",
                duration: 5000,
                isClosable: true,
            });
        }
    }, [loadDailyData, loadChartData, toast]);

    // Chart data preparation
    const prepareSectorComparisonData = () => {
        // Mock data for sector comparison
        const sectorNames = sectors.map(sector => 
            sector.replace('MASI_', '').replace(/_/g, ' ')
        ).slice(0, 8);
        
        return {
            labels: sectorNames,
            datasets: [{
                label: 'Performance (%)',
                data: sectorNames.map(() => (Math.random() - 0.5) * 10),
                backgroundColor: [
                    '#ffce30', '#ff6b6b', '#4ecdc4', '#45b7d1',
                    '#96ceb4', '#ffeaa7', '#dda0dd', '#98d8c8'
                ],
                borderWidth: 1
            }]
        };
    };

    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    color: '#ffffff',
                    usePointStyle: true
                }
            },
            title: {
                display: true,
                text: 'Market Data Visualization',
                color: '#ffce30',
                font: {
                    size: 16,
                    weight: 'bold'
                }
            }
        },
        scales: {
            x: {
                grid: {
                    color: '#444444'
                },
                ticks: {
                    color: '#ffffff'
                }
            },
            y: {
                grid: {
                    color: '#444444'
                },
                ticks: {
                    color: '#ffffff'
                }
            }
        }
    };

    // Market Statistics Calculation Functions
    const calculateTotalMarketCap = () => {
        // Mock calculation based on available indices
        const baseCap = 650000000000; // 650B MAD
        const variation = dailyData.indices.length > 0 
            ? parseFloat(dailyData.indices[0]?.variation_veille || 0) 
            : 0;
        const adjustedCap = baseCap * (1 + variation / 100);
        
        return `${(adjustedCap / 1000000000).toFixed(1)}B MAD`;
    };

    const calculateAvgVolume = () => {
        // Mock average volume calculation
        const baseVolume = 245000000; // 245M MAD
        const variance = (Math.random() - 0.5) * 50000000;
        const avgVolume = baseVolume + variance;
        
        return `${(avgVolume / 1000000).toFixed(1)}M MAD`;
    };

    const getMarketSentiment = () => {
        const gainers = dailyData.hausses.length;
        const losers = dailyData.baisses.length;
        
        if (gainers > losers * 1.5) return 'Bullish';
        if (losers > gainers * 1.5) return 'Bearish';
        return 'Neutral';
    };

    const getMarketSentimentColor = () => {
        const sentiment = getMarketSentiment();
        switch (sentiment) {
            case 'Bullish': return '#4ade80';
            case 'Bearish': return '#f87171';
            default: return '#ffce30';
        }
    };

    // Enhanced refresh function using backend APIs
    const refreshData = handleRefresh;

    // Debug: Log current state before render
    console.log('Render debug - dailyData:', dailyData);
    console.log('Render debug - hausses length:', dailyData.hausses?.length);
    console.log('Render debug - baisses length:', dailyData.baisses?.length);
    console.log('Render debug - volumeData:', volumeData);

    return (
        <Box p={6} bg="#0a0a0a" minH="100vh" color="#ffffff">
            {/* Header */}
            <Flex justify="space-between" align="center" mb={6}>
                <VStack align="start" spacing={1}>
                    <Text fontSize="2xl" fontWeight="bold" color="#ffce30">
                        Market Data Visualization
                    </Text>
                    <Text fontSize="sm" color="#888">
                        Comprehensive MASI indices and sectoral analysis
                    </Text>
                </VStack>
                
                <HStack spacing={4}>
                    <IconButton
                        icon={<FiRefreshCw />}
                        onClick={refreshData}
                        colorScheme="yellow"
                        variant="outline"
                        aria-label="Refresh data"
                    />
                    
                    <IconButton
                        icon={<FiDownload />}
                        colorScheme="yellow"
                        variant="outline"
                        aria-label="Export data"
                    />
                </HStack>
            </Flex>

            <Tabs index={activeTab} onChange={setActiveTab} colorScheme="yellow">
                <TabList>
                    <Tab>Market Overview</Tab>
                    <Tab>MASI Indices</Tab>
                    <Tab>Sector Analysis</Tab>
                    <Tab>Daily Movers</Tab>
                    <Tab>Historical Data</Tab>
                </TabList>

                <TabPanels>
                    {/* Market Overview Tab */}
                    <TabPanel>
                        {/* Index Cards */}
                        <Grid templateColumns="repeat(3, 1fr)" gap={6} mb={6}>
                            {dailyData.indices.map((index, idx) => (
                                <GridItem key={idx}>
                                    <Card bg="#1a1a1a" border="1px solid #333">
                                        <CardBody>
                                            <Stat>
                                                <StatLabel color="#888">{index.libelle}</StatLabel>
                                                <StatNumber color="#ffffff">
                                                    {parseFloat(index.valeur_actuelle).toLocaleString()}
                                                </StatNumber>
                                                <StatHelpText color={parseFloat(index.variation_veille) >= 0 ? '#4ade80' : '#f87171'}>
                                                    <StatArrow 
                                                        type={parseFloat(index.variation_veille) >= 0 ? 'increase' : 'decrease'} 
                                                    />
                                                    {index.variation_veille}% (Daily)
                                                </StatHelpText>
                                                <StatHelpText color="#ffce30">
                                                    +{index.variation_annuelle}% (YTD)
                                                </StatHelpText>
                                            </Stat>
                                        </CardBody>
                                    </Card>
                                </GridItem>
                            ))}
                        </Grid>

                        {/* Charts and Statistics Section */}
                        <Grid templateColumns="repeat(2, 1fr)" gap={6} mb={6}>
                            {/* Market Performance Chart */}
                            <GridItem>
                                <Card bg="#1a1a1a" border="1px solid #333">
                                    <CardHeader>
                                        <Text fontSize="lg" fontWeight="bold" color="#ffce30">
                                            Market Performance Trend
                                        </Text>
                                    </CardHeader>
                                    <CardBody>
                                        <Box h="350px">
                                            {marketTrendData && (
                                                <Line 
                                                    data={marketTrendData} 
                                                    options={{
                                                        ...chartOptions,
                                                        plugins: {
                                                            ...chartOptions.plugins,
                                                            title: {
                                                                ...chartOptions.plugins.title,
                                                                text: 'MASI Index Performance (30 Days)'
                                                            }
                                                        }
                                                    }} 
                                                />
                                            )}
                                        </Box>
                                    </CardBody>
                                </Card>
                            </GridItem>

                            {/* Volume Analysis Chart */}
                            <GridItem>
                                <Card bg="#1a1a1a" border="1px solid #333">
                                    <CardHeader>
                                        <Text fontSize="lg" fontWeight="bold" color="#ffce30">
                                            Trading Volume Analysis
                                        </Text>
                                    </CardHeader>
                                    <CardBody>
                                        <Box h="350px">
                                            {volumeData && (
                                                <Bar 
                                                    data={volumeData} 
                                                    options={{
                                                        ...chartOptions,
                                                        plugins: {
                                                            ...chartOptions.plugins,
                                                            title: {
                                                                ...chartOptions.plugins.title,
                                                                text: 'Daily Trading Volume (Last 7 Days)'
                                                            }
                                                        }
                                                    }} 
                                                />
                                            )}
                                        </Box>
                                    </CardBody>
                                </Card>
                            </GridItem>
                        </Grid>

                        {/* Market Statistics */}
                        <Grid templateColumns="repeat(4, 1fr)" gap={6} mb={6}>
                            <GridItem>
                                <Card bg="#1a1a1a" border="1px solid #333">
                                    <CardBody>
                                        <Stat>
                                            <StatLabel color="#888">Market Cap</StatLabel>
                                            <StatNumber color="#ffffff">
                                                {calculateTotalMarketCap()}
                                            </StatNumber>
                                            <StatHelpText color="#4ade80">
                                                Total Market Capitalization
                                            </StatHelpText>
                                        </Stat>
                                    </CardBody>
                                </Card>
                            </GridItem>
                            
                            <GridItem>
                                <Card bg="#1a1a1a" border="1px solid #333">
                                    <CardBody>
                                        <Stat>
                                            <StatLabel color="#888">Avg Daily Volume</StatLabel>
                                            <StatNumber color="#ffffff">
                                                {calculateAvgVolume()}
                                            </StatNumber>
                                            <StatHelpText color="#ffce30">
                                                7-Day Average
                                            </StatHelpText>
                                        </Stat>
                                    </CardBody>
                                </Card>
                            </GridItem>
                            
                            <GridItem>
                                <Card bg="#1a1a1a" border="1px solid #333">
                                    <CardBody>
                                        <Stat>
                                            <StatLabel color="#888">Active Stocks</StatLabel>
                                            <StatNumber color="#ffffff">
                                                {dailyData.hausses.length + dailyData.baisses.length}
                                            </StatNumber>
                                            <StatHelpText color="#4ade80">
                                                Gainers: {dailyData.hausses.length}
                                            </StatHelpText>
                                            <StatHelpText color="#f87171">
                                                Losers: {dailyData.baisses.length}
                                            </StatHelpText>
                                        </Stat>
                                    </CardBody>
                                </Card>
                            </GridItem>
                            
                            <GridItem>
                                <Card bg="#1a1a1a" border="1px solid #333">
                                    <CardBody>
                                        <Stat>
                                            <StatLabel color="#888">Market Sentiment</StatLabel>
                                            <StatNumber color={getMarketSentimentColor()}>
                                                {getMarketSentiment()}
                                            </StatNumber>
                                            <StatHelpText color="#888">
                                                Based on daily movers
                                            </StatHelpText>
                                        </Stat>
                                    </CardBody>
                                </Card>
                            </GridItem>
                        </Grid>

                        {/* Top Performers Section */}
                        <Grid templateColumns="repeat(2, 1fr)" gap={6}>
                            {/* Top Gainers */}
                            <GridItem>
                                <Card bg="#1a1a1a" border="1px solid #333">
                                    <CardHeader>
                                        <Text fontSize="lg" fontWeight="bold" color="#4ade80">
                                            Top Gainers Today
                                        </Text>
                                    </CardHeader>
                                    <CardBody>
                                        <TableContainer>
                                            <Table size="sm">
                                                <Thead>
                                                    <Tr>
                                                        <Th color="#888">Stock</Th>
                                                        <Th color="#888">Price</Th>
                                                        <Th color="#888">Change</Th>
                                                    </Tr>
                                                </Thead>
                                                <Tbody>
                                                    {dailyData.hausses.slice(0, 5).map((stock, idx) => (
                                                        <Tr key={idx}>
                                                            <Td color="#ffffff">{stock.libelle}</Td>
                                                            <Td color="#ffffff">{parseFloat(stock.cours_courant).toFixed(2)}</Td>
                                                            <Td color="#4ade80">+{stock.variation_rel}%</Td>
                                                        </Tr>
                                                    ))}
                                                </Tbody>
                                            </Table>
                                        </TableContainer>
                                    </CardBody>
                                </Card>
                            </GridItem>

                            {/* Top Losers */}
                            <GridItem>
                                <Card bg="#1a1a1a" border="1px solid #333">
                                    <CardHeader>
                                        <Text fontSize="lg" fontWeight="bold" color="#f87171">
                                            Top Losers Today
                                        </Text>
                                    </CardHeader>
                                    <CardBody>
                                        <TableContainer>
                                            <Table size="sm">
                                                <Thead>
                                                    <Tr>
                                                        <Th color="#888">Stock</Th>
                                                        <Th color="#888">Price</Th>
                                                        <Th color="#888">Change</Th>
                                                    </Tr>
                                                </Thead>
                                                <Tbody>
                                                    {dailyData.baisses.slice(0, 5).map((stock, idx) => (
                                                        <Tr key={idx}>
                                                            <Td color="#ffffff">{stock.libelle}</Td>
                                                            <Td color="#ffffff">{parseFloat(stock.cours_courant).toFixed(2)}</Td>
                                                            <Td color="#f87171">{stock.variation_rel}%</Td>
                                                        </Tr>
                                                    ))}
                                                </Tbody>
                                            </Table>
                                        </TableContainer>
                                    </CardBody>
                                </Card>
                            </GridItem>
                        </Grid>
                    </TabPanel>

                    {/* MASI Indices Tab */}
                    <TabPanel>
                        <MasiIndicesVisualization />
                    </TabPanel>

                    {/* Sector Analysis Tab */}
                    <TabPanel>
                        <Card bg="#1a1a1a" border="1px solid #333">
                            <CardHeader>
                                <Text fontSize="lg" fontWeight="bold" color="#ffce30">
                                    Sector Performance Comparison
                                </Text>
                            </CardHeader>
                            <CardBody>
                                <Box h="500px">
                                    <Bar 
                                        data={prepareSectorComparisonData()} 
                                        options={{
                                            ...chartOptions,
                                            plugins: {
                                                ...chartOptions.plugins,
                                                title: {
                                                    ...chartOptions.plugins.title,
                                                    text: 'Sector Performance (%)'
                                                }
                                            }
                                        }} 
                                    />
                                </Box>
                            </CardBody>
                        </Card>
                    </TabPanel>

                    {/* Daily Movers Tab */}
                    <TabPanel>
                        <Grid templateColumns="repeat(2, 1fr)" gap={6}>
                            {/* Top Gainers */}
                            <GridItem>
                                <Card bg="#1a1a1a" border="1px solid #333">
                                    <CardHeader>
                                        <HStack>
                                            <FiTrendingUp color="#4ade80" />
                                            <Text fontSize="lg" fontWeight="bold" color="#4ade80">
                                                Top Gainers
                                            </Text>
                                        </HStack>
                                    </CardHeader>
                                    <CardBody>
                                        <TableContainer>
                                            <Table size="sm">
                                                <Thead>
                                                    <Tr>
                                                        <Th color="#888">Stock</Th>
                                                        <Th color="#888">Change (%)</Th>
                                                        <Th color="#888">Volume</Th>
                                                    </Tr>
                                                </Thead>
                                                <Tbody>
                                                    {dailyData.hausses.map((stock, idx) => (
                                                        <Tr key={idx}>
                                                            <Td color="#ffffff">{stock.libelle}</Td>
                                                            <Td color="#4ade80">+{stock.variation_rel}%</Td>
                                                            <Td color="#888">
                                                                {parseFloat(stock.volume_echange).toLocaleString()}
                                                            </Td>
                                                        </Tr>
                                                    ))}
                                                </Tbody>
                                            </Table>
                                        </TableContainer>
                                    </CardBody>
                                </Card>
                            </GridItem>

                            {/* Top Losers */}
                            <GridItem>
                                <Card bg="#1a1a1a" border="1px solid #333">
                                    <CardHeader>
                                        <HStack>
                                            <FiTrendingDown color="#f87171" />
                                            <Text fontSize="lg" fontWeight="bold" color="#f87171">
                                                Top Losers
                                            </Text>
                                        </HStack>
                                    </CardHeader>
                                    <CardBody>
                                        <TableContainer>
                                            <Table size="sm">
                                                <Thead>
                                                    <Tr>
                                                        <Th color="#888">Stock</Th>
                                                        <Th color="#888">Change (%)</Th>
                                                        <Th color="#888">Volume</Th>
                                                    </Tr>
                                                </Thead>
                                                <Tbody>
                                                    {dailyData.baisses.map((stock, idx) => (
                                                        <Tr key={idx}>
                                                            <Td color="#ffffff">{stock.libelle}</Td>
                                                            <Td color="#f87171">{stock.variation_rel}%</Td>
                                                            <Td color="#888">
                                                                {parseFloat(stock.volume_echange).toLocaleString()}
                                                            </Td>
                                                        </Tr>
                                                    ))}
                                                </Tbody>
                                            </Table>
                                        </TableContainer>
                                    </CardBody>
                                </Card>
                            </GridItem>
                        </Grid>
                    </TabPanel>

                    {/* Historical Data Tab */}
                    <TabPanel>
                        <VStack spacing={6}>
                            <Text fontSize="lg" color="#ffce30">
                                Historical Data Management
                            </Text>
                            <Text color="#888" textAlign="center">
                                Access to comprehensive historical data including:
                                <br />
                                • MASI indices historical performance
                                <br />
                                • Sectoral indices data
                                <br />
                                • Daily market movements
                                <br />
                                • Scraping logs and data quality reports
                            </Text>
                            
                            <Button 
                                colorScheme="yellow" 
                                leftIcon={<FiDownload />}
                                onClick={() => toast({
                                    title: 'Export Started',
                                    description: 'Historical data export is being prepared',
                                    status: 'info',
                                    duration: 3000
                                })}
                            >
                                Export Historical Data
                            </Button>
                        </VStack>
                    </TabPanel>
                </TabPanels>
            </Tabs>
        </Box>
    );
};

export default Dashboard;
