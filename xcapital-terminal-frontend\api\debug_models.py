#!/usr/bin/env python3
"""
Debug script to investigate ARIMA model pickle files
"""

import pickle
import os
import sys
from pathlib import Path

def debug_pickle_file(file_path):
    """Debug a pickle file to understand its structure"""
    print(f"\n🔍 Analyzing pickle file: {file_path}")
    
    try:
        # Check file size
        file_size = os.path.getsize(file_path)
        print(f"   📊 File size: {file_size} bytes")
        
        # Read first few bytes to check pickle protocol
        with open(file_path, 'rb') as f:
            first_bytes = f.read(10)
            print(f"   🔢 First 10 bytes: {first_bytes}")
            
        # Try to load with different approaches
        print("   🔄 Attempting to load...")
        
        # Method 1: Standard pickle load
        try:
            with open(file_path, 'rb') as f:
                model = pickle.load(f)
            print(f"   ✅ Standard pickle.load() successful")
            print(f"   📋 Model type: {type(model)}")
            print(f"   📋 Model attributes: {dir(model)[:10]}...")  # First 10 attributes
            return model
        except Exception as e1:
            print(f"   ❌ Standard pickle.load() failed: {e1}")
            
            # Method 2: Try with different encoding
            try:
                with open(file_path, 'rb') as f:
                    model = pickle.load(f, encoding='latin1')
                print(f"   ✅ pickle.load() with latin1 encoding successful")
                print(f"   📋 Model type: {type(model)}")
                return model
            except Exception as e2:
                print(f"   ❌ pickle.load() with latin1 failed: {e2}")
                
                # Method 3: Try to load as binary and check if it's compressed
                try:
                    import gzip
                    with gzip.open(file_path, 'rb') as f:
                        model = pickle.load(f)
                    print(f"   ✅ Gzip compressed pickle successful")
                    print(f"   📋 Model type: {type(model)}")
                    return model
                except Exception as e3:
                    print(f"   ❌ Gzip compressed pickle failed: {e3}")
                    
                    # Method 4: Try with joblib (sometimes used for sklearn models)
                    try:
                        import joblib
                        model = joblib.load(file_path)
                        print(f"   ✅ Joblib load successful")
                        print(f"   📋 Model type: {type(model)}")
                        return model
                    except Exception as e4:
                        print(f"   ❌ Joblib load failed: {e4}")
                        print(f"   ⚠️ All loading methods failed")
                        return None
                        
    except Exception as e:
        print(f"   💥 Fatal error analyzing file: {e}")
        return None

def main():
    print("🔬 ARIMA Model Debug Analysis")
    print("=" * 50)
    
    models_dir = Path("../models/Arima")
    
    if not models_dir.exists():
        print(f"❌ Models directory not found: {models_dir}")
        return
    
    # Get first few model files to test
    model_files = list(models_dir.glob("*_arima_model.pkl"))[:3]
    
    if not model_files:
        print(f"❌ No ARIMA model files found in {models_dir}")
        return
    
    print(f"📁 Found {len(model_files)} model files to analyze")
    
    successful_models = []
    
    for model_file in model_files:
        model = debug_pickle_file(model_file)
        if model is not None:
            successful_models.append((model_file.name, model))
    
    print(f"\n📊 Summary:")
    print(f"   ✅ Successfully loaded: {len(successful_models)} models")
    print(f"   ❌ Failed to load: {len(model_files) - len(successful_models)} models")
    
    if successful_models:
        print(f"\n📋 Successfully loaded models:")
        for filename, model in successful_models:
            print(f"   • {filename}: {type(model)}")
            
            # Try to get model information
            if hasattr(model, 'summary'):
                try:
                    print(f"     Summary available: Yes")
                except:
                    print(f"     Summary available: Error")
            
            if hasattr(model, 'forecast'):
                print(f"     Forecast method: Available")
            elif hasattr(model, 'predict'):
                print(f"     Predict method: Available")
            else:
                print(f"     Prediction methods: None found")

if __name__ == "__main__":
    main()
