from django.db import models
from decimal import Decimal
from django.core.validators import MinValueValidator


class FinancialInstrument(models.Model):
    """Model pour les instruments financiers de la Bourse de Casablanca"""
    symbol = models.CharField(max_length=50, unique=True, primary_key=True)
    label = models.CharField(max_length=200)
    sector = models.CharField(max_length=100)
    data_file = models.CharField(max_length=200)
    predictions_file = models.CharField(max_length=200, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    market_cap = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'financial_instruments'
        ordering = ['symbol']

    def __str__(self):
        return f"{self.symbol} - {self.label}"

    @property
    def has_predictions(self):
        return self.predictions_file is not None


class InstrumentPrice(models.Model):
    """Model pour les prix des instruments financiers"""
    instrument = models.ForeignKey(FinancialInstrument, on_delete=models.CASCADE, related_name='prices')
    date = models.DateField()
    open_price = models.DecimalField(max_digits=15, decimal_places=4, validators=[MinValueValidator(Decimal('0.0001'))])
    high_price = models.DecimalField(max_digits=15, decimal_places=4, validators=[MinValueValidator(Decimal('0.0001'))])
    low_price = models.DecimalField(max_digits=15, decimal_places=4, validators=[MinValueValidator(Decimal('0.0001'))])
    close_price = models.DecimalField(max_digits=15, decimal_places=4, validators=[MinValueValidator(Decimal('0.0001'))])
    current_price = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    volume = models.BigIntegerField(validators=[MinValueValidator(0)])
    shares_traded = models.BigIntegerField(null=True, blank=True)
    total_trades = models.IntegerField(null=True, blank=True)
    market_cap = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    adjusted_close = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    consolidated_ratio = models.DecimalField(max_digits=10, decimal_places=6, null=True, blank=True)
    
    class Meta:
        db_table = 'instrument_prices'
        unique_together = ['instrument', 'date']
        ordering = ['-date']
        indexes = [
            models.Index(fields=['instrument', 'date']),
            models.Index(fields=['date']),
            models.Index(fields=['instrument', '-date']),
        ]

    def __str__(self):
        return f"{self.instrument.symbol} - {self.date}"


class MASIIndex(models.Model):
    """Model pour les indices MASI"""
    INDEX_TYPES = [
        ('MAIN', 'Main Index'),
        ('BLUE_CHIP', 'Blue Chip Index'),
        ('SECTORAL', 'Sectoral Index'),
    ]
    
    symbol = models.CharField(max_length=100, unique=True, primary_key=True)
    label = models.CharField(max_length=200)
    type = models.CharField(max_length=20, choices=INDEX_TYPES)
    description = models.TextField()
    data_file = models.CharField(max_length=200)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'masi_indices'
        ordering = ['symbol']

    def __str__(self):
        return f"{self.symbol} - {self.label}"


class MASIIndexValue(models.Model):
    """Model pour les valeurs des indices MASI"""
    index = models.ForeignKey(MASIIndex, on_delete=models.CASCADE, related_name='values')
    date = models.DateField()
    value = models.DecimalField(max_digits=15, decimal_places=4)
    previous_value = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    daily_change = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    daily_change_pct = models.DecimalField(max_digits=8, decimal_places=4, null=True, blank=True)
    
    class Meta:
        db_table = 'masi_index_values'
        unique_together = ['index', 'date']
        ordering = ['-date']
        indexes = [
            models.Index(fields=['index', 'date']),
            models.Index(fields=['date']),
            models.Index(fields=['index', '-date']),
        ]

    def __str__(self):
        return f"{self.index.symbol} - {self.date} - {self.value}"


class InstrumentPrediction(models.Model):
    """Model pour les prédictions ARIMA des instruments"""
    instrument = models.ForeignKey(FinancialInstrument, on_delete=models.CASCADE, related_name='predictions')
    date = models.DateField()
    predicted_price = models.DecimalField(max_digits=15, decimal_places=4)
    lower_ci = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    upper_ci = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'instrument_predictions'
        unique_together = ['instrument', 'date']
        ordering = ['-date']
        indexes = [
            models.Index(fields=['instrument', 'date']),
            models.Index(fields=['date']),
        ]

    def __str__(self):
        return f"{self.instrument.symbol} - {self.date} - {self.predicted_price}"
