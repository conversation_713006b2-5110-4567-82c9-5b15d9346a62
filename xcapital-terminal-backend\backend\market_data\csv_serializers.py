from rest_framework import serializers
from .csv_models import FinancialInstrument, InstrumentPrice, MASIIndex, MASIIndexValue, InstrumentPrediction
from datetime import datetime, date


class PriceDataRequestSerializer(serializers.Serializer):
    """Serializer pour valider les requêtes de données de prix via POST"""
    
    symbol = serializers.CharField(
        max_length=50,
        help_text="Symbole de l'instrument (ex: ATTIJARIWAFA_BANK)"
    )
    
    # Période prédéfinie ou dates personnalisées
    period = serializers.ChoiceField(
        choices=[
            ('1M', '1 Mois'),
            ('3M', '3 Mois'), 
            ('6M', '6 Mois'),
            ('1Y', '1 Année'),
            ('3Y', '3 Années'),
            ('ALL', 'Toutes les données'),
            ('CUSTOM', 'Période personnalisée')
        ],
        required=False,
        help_text="Période prédéfinie"
    )
    
    start_date = serializers.DateField(
        required=False,
        help_text="Date de début (YYYY-MM-DD). Ne peut pas être antérieure au 15-08-2022"
    )
    
    end_date = serializers.DateField(
        required=False,
        help_text="Date de fin (YYYY-MM-DD). Par défaut aujourd'hui"
    )
    
    def validate(self, data):
        """Validation globale des données"""
        period = data.get('period')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        # Si période personnalisée, les dates sont obligatoires
        if period == 'CUSTOM':
            if not start_date:
                raise serializers.ValidationError(
                    "start_date est obligatoire pour une période personnalisée"
                )
        
        # Validation de la date minimale
        min_date = date(2022, 8, 15)
        if start_date and start_date < min_date:
            raise serializers.ValidationError(
                f"La date de début ne peut pas être antérieure au {min_date.strftime('%d-%m-%Y')}"
            )
        
        # Validation de la date maximale (aujourd'hui)
        today = date.today()
        if end_date and end_date > today:
            raise serializers.ValidationError(
                f"La date de fin ne peut pas être supérieure à aujourd'hui ({today.strftime('%d-%m-%Y')})"
            )
        
        # Validation de l'ordre des dates
        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError(
                "La date de début doit être antérieure à la date de fin"
            )
        
        # Si pas de période spécifiée, utiliser ALL par défaut
        if not period:
            data['period'] = 'ALL'
        
        return data
    
    def validate_symbol(self, value):
        """Validation du symbole d'instrument"""
        try:
            instrument = FinancialInstrument.objects.get(symbol=value, is_active=True)
            return value
        except FinancialInstrument.DoesNotExist:
            raise serializers.ValidationError(
                f"Instrument '{value}' non trouvé ou inactif"
            )


class FinancialInstrumentSerializer(serializers.ModelSerializer):
    has_predictions = serializers.ReadOnlyField()
    latest_price = serializers.SerializerMethodField()
    price_change = serializers.SerializerMethodField()
    
    class Meta:
        model = FinancialInstrument
        fields = ['symbol', 'label', 'sector', 'data_file', 'predictions_file', 
                 'is_active', 'market_cap', 'has_predictions', 'latest_price', 'price_change']
    
    def get_latest_price(self, obj):
        try:
            latest = obj.prices.first()
            return {
                'date': latest.date if latest else None,
                'close_price': float(latest.close_price) if latest else None,
                'volume': latest.volume if latest else None
            } if latest else None
        except:
            return None
    
    def get_price_change(self, obj):
        try:
            prices = obj.prices.order_by('-date')[:2]
            if len(prices) >= 2:
                current = float(prices[0].close_price)
                previous = float(prices[1].close_price)
                change = current - previous
                change_pct = (change / previous) * 100 if previous != 0 else 0
                return {
                    'absolute': round(change, 4),
                    'percentage': round(change_pct, 2)
                }
            return None
        except:
            return None


class InstrumentPriceSerializer(serializers.ModelSerializer):
    symbol = serializers.CharField(source='instrument.symbol', read_only=True)
    
    class Meta:
        model = InstrumentPrice
        fields = ['date', 'symbol', 'open_price', 'high_price', 'low_price', 
                 'close_price', 'current_price', 'volume', 'shares_traded', 
                 'total_trades', 'market_cap', 'adjusted_close', 'consolidated_ratio']


class InstrumentPriceRangeSerializer(serializers.ModelSerializer):
    """Serializer pour les données de prix avec plage de dates"""
    class Meta:
        model = InstrumentPrice
        fields = ['date', 'open_price', 'high_price', 'low_price', 'close_price', 'volume']


class MASIIndexSerializer(serializers.ModelSerializer):
    latest_value = serializers.SerializerMethodField()
    index_change = serializers.SerializerMethodField()
    
    class Meta:
        model = MASIIndex
        fields = ['symbol', 'label', 'type', 'description', 'data_file', 
                 'is_active', 'latest_value', 'index_change']
    
    def get_latest_value(self, obj):
        latest = obj.values.first()
        return {
            'date': latest.date if latest else None,
            'value': float(latest.value) if latest else None,
            'daily_change': float(latest.daily_change) if latest and latest.daily_change else None,
            'daily_change_pct': float(latest.daily_change_pct) if latest and latest.daily_change_pct else None
        } if latest else None
    
    def get_index_change(self, obj):
        values = obj.values.order_by('-date')[:2]
        if len(values) >= 2:
            current = float(values[0].value)
            previous = float(values[1].value)
            change = current - previous
            change_pct = (change / previous) * 100 if previous != 0 else 0
            return {
                'absolute': round(change, 4),
                'percentage': round(change_pct, 2)
            }
        return None


class MASIIndexValueSerializer(serializers.ModelSerializer):
    index_symbol = serializers.CharField(source='index.symbol', read_only=True)
    
    class Meta:
        model = MASIIndexValue
        fields = ['date', 'index_symbol', 'value', 'previous_value', 
                 'daily_change', 'daily_change_pct']


class InstrumentPredictionSerializer(serializers.ModelSerializer):
    symbol = serializers.CharField(source='instrument.symbol', read_only=True)
    
    class Meta:
        model = InstrumentPrediction
        fields = ['date', 'symbol', 'predicted_price', 'lower_ci', 'upper_ci', 'created_at']


class SectorAnalysisSerializer(serializers.Serializer):
    """Serializer pour l'analyse sectorielle"""
    sector = serializers.CharField()
    instruments_count = serializers.IntegerField()
    total_market_cap = serializers.DecimalField(max_digits=20, decimal_places=2)
    avg_price = serializers.DecimalField(max_digits=15, decimal_places=4)
    total_volume = serializers.IntegerField()
    top_performers = serializers.ListField()


class MarketOverviewSerializer(serializers.Serializer):
    """Serializer pour la vue d'ensemble du marché"""
    total_instruments = serializers.IntegerField()
    active_instruments = serializers.IntegerField()
    total_indices = serializers.IntegerField()
    market_cap = serializers.DecimalField(max_digits=20, decimal_places=2)
    total_volume = serializers.IntegerField()
    top_gainers = serializers.ListField()
    top_losers = serializers.ListField()
    sectors_performance = serializers.ListField()


class InstrumentDetailSerializer(serializers.ModelSerializer):
    """Serializer détaillé pour un instrument spécifique"""
    latest_prices = serializers.SerializerMethodField()
    predictions = serializers.SerializerMethodField()
    technical_indicators = serializers.SerializerMethodField()
    
    class Meta:
        model = FinancialInstrument
        fields = ['symbol', 'label', 'sector', 'market_cap', 'has_predictions',
                 'latest_prices', 'predictions', 'technical_indicators']
    
    def get_latest_prices(self, obj):
        prices = obj.prices.order_by('-date')[:30]  # 30 derniers jours
        return InstrumentPriceRangeSerializer(prices, many=True).data
    
    def get_predictions(self, obj):
        if obj.has_predictions:
            predictions = obj.predictions.order_by('-date')[:15]  # 15 prochains jours
            return InstrumentPredictionSerializer(predictions, many=True).data
        return None
    
    def get_technical_indicators(self, obj):
        # Calculer des indicateurs techniques basiques
        prices = obj.prices.order_by('-date')[:20]
        if len(prices) >= 10:
            prices_list = [float(p.close_price) for p in prices]
            sma_10 = sum(prices_list[:10]) / 10
            sma_20 = sum(prices_list[:20]) / 20 if len(prices_list) >= 20 else None
            
            # Calcul RSI simplifié
            gains = []
            losses = []
            for i in range(1, min(15, len(prices_list))):
                change = prices_list[i-1] - prices_list[i]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))
            
            avg_gain = sum(gains) / len(gains) if gains else 0
            avg_loss = sum(losses) / len(losses) if losses else 0
            rsi = 100 - (100 / (1 + (avg_gain / avg_loss))) if avg_loss != 0 else 50
            
            return {
                'sma_10': round(sma_10, 4),
                'sma_20': round(sma_20, 4) if sma_20 else None,
                'rsi': round(rsi, 2),
                'current_price': float(prices[0].close_price),
                'volume_avg': sum([p.volume for p in prices[:10]]) // 10
            }
        return None
