@echo off
echo ================================================
echo XCapital Terminal - ARIMA Integration Setup
echo ================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js 16+ and try again
    pause
    exit /b 1
)

echo Both Python and Node.js are installed.
echo.

REM Check directories
echo Checking project structure...
if not exist "api\main.py" (
    echo Error: API files not found. Please ensure api\main.py exists.
    pause
    exit /b 1
)

if not exist "models\Arima" (
    echo Warning: ARIMA models directory not found at models\Arima
    echo Please ensure ARIMA model files (.pkl) are in this directory
)

if not exist "data" (
    echo Warning: Data directory not found
    echo Please ensure historical data CSV files are in the data directory
)

echo.
echo ================================================
echo Setup Instructions:
echo ================================================
echo.
echo 1. Install Python dependencies for API:
echo    cd api
echo    pip install -r requirements.txt
echo.
echo 2. Install Node.js dependencies for frontend:
echo    npm install
echo.
echo 3. Start the API server:
echo    cd api
echo    python main.py
echo    (API will run on http://localhost:8000)
echo.
echo 4. Start the frontend:
echo    npm start
echo    (Frontend will run on http://localhost:3000)
echo.
echo ================================================
echo Quick Start Commands:
echo ================================================
echo.
echo Backend: api\start_api.bat
echo Frontend: start_frontend.bat
echo.
echo ================================================
echo Testing the Integration:
echo ================================================
echo.
echo 1. Open http://localhost:3000 in your browser
echo 2. Navigate to the Unified Prediction Interface
echo 3. Select a ticker (e.g., ATW, GAZ)
echo 4. Choose "Equity Price Modeling" as analysis type
echo 5. Set dates and time horizon
echo 6. Click "Generate Analytics Report"
echo.
echo Available ARIMA models:
if exist "models\Arima\*.pkl" (
    dir /b "models\Arima\*_arima_model.pkl" | findstr /v "Command" | head -10
) else (
    echo No ARIMA model files found
)
echo.

pause
