"""Legacy market/csv/simple API views removed.

This module intentionally left minimal to avoid import errors from residual references.
If any legacy import persists, raise a clear exception to guide cleanup.
"""

from rest_framework.decorators import api_view
from rest_framework.response import Response

@api_view(['GET'])
def api_v1_root(request):  # Minimal root retained after legacy purge
    return Response({
        'message': 'XCapital Terminal - API v1 (reduced)',
        'version': '2.0.0',
        'active': {
            'predictions': '/api/v1/predictions/',
            'xcapital': '/api/v1/xcapital/'
        }
    })

@api_view(['GET'])
def legacy_removed(request):  # pragma: no cover
    return Response({'error': 'Legacy endpoints removed', 'status': 'gone'}, status=410)
