@echo off
echo Starting XCapital Terminal ARIMA Prediction API...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if we're in the correct directory
if not exist "main.py" (
    echo Error: main.py not found in current directory
    echo Please run this script from the api directory
    pause
    exit /b 1
)

REM Install dependencies if requirements.txt exists
if exist "requirements.txt" (
    echo Installing Python dependencies...
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
    echo.
)

REM Check if models directory exists
if not exist "..\models\Arima" (
    echo Warning: Models directory not found at ..\models\Arima
    echo Please ensure ARIMA models are properly placed
    echo.
)

REM Check if data directory exists
if not exist "..\data" (
    echo Warning: Data directory not found at ..\data
    echo Please ensure historical data CSV files are properly placed
    echo.
)

echo Starting FastAPI server...
echo API will be available at: http://localhost:8000
echo API documentation: http://localhost:8000/docs
echo.
echo Press Ctrl+C to stop the server
echo.

python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
